from app import db

class Menu(db.Model):
    __tablename__ = 'menus'
    
    id = db.Column(db.String(50), primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    path = db.Column(db.String(200), nullable=False)
    parent_id = db.Column(db.String(50), nullable=True)
    order_num = db.Column(db.Integer, default=0)
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'path': self.path
        } 