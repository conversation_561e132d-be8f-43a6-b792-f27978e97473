from app import db
from datetime import datetime

class OracleServer(db.Model):
    __tablename__ = 'oracle_servers'
    id = db.<PERSON>umn(db.Integer, primary_key=True, autoincrement=True)
    server_name = db.Column(db.String(100), nullable=False)
    ssh_address = db.Column(db.String(50), nullable=False)
    ssh_port = db.Column(db.Integer, default=22)
    ssh_username = db.Column(db.String(50), default='root')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    status = db.Column(db.Boolean, default=True)
    db_address = db.Column(db.String(50), nullable=False)
    db_port = db.Column(db.Integer, default=1521, nullable=False)
    db_username = db.Column(db.String(50), default='paymentdb')
    db_password = db.Column(db.String(100), default='paymentdb2019@ORCL')

    def __repr__(self):
        return f'<OracleServer {self.server_name} ({self.ip_address})>'

    def to_dict(self):
        return {
            'id': self.id,
            'server_name': self.server_name,
            'ssh_address': self.ssh_address,
            'ssh_port': self.ssh_port,
            'ssh_username': self.ssh_username,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            'status': self.status,
            'db_address': self.db_address,
            'db_port': self.db_port,
            'db_username': self.db_username,
            'db_password': self.db_password
        }