from flask import Blueprint, jsonify
from flask_jwt_extended import jwt_required
from app.models.menu import Menu

menu_bp = Blueprint('menu', __name__)

@menu_bp.route('/user/menus', methods=['GET'])
@jwt_required()
def get_menus():
    # 获取所有菜单
    all_menus = Menu.query.order_by(Menu.order_num).all()
    
    # 构建菜单树
    menu_dict = {}
    menu_tree = []
    
    # 首先将所有菜单放入字典中
    for menu in all_menus:
        menu_data = menu.to_dict()
        menu_data['children'] = []
        menu_dict[menu.id] = menu_data
    
    # 构建树形结构
    for menu in all_menus:
        if menu.parent_id:
            # 如果有父菜单，添加到父菜单的children中
            parent = menu_dict.get(menu.parent_id)
            if parent:
                parent['children'].append(menu_dict[menu.id])
        else:
            # 如果是顶级菜单，直接添加到结果列表
            menu_tree.append(menu_dict[menu.id])
    
    return jsonify({
        'code': 200,
        'message': 'success',
        'data': menu_tree
    }) 