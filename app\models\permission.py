from app import db
from datetime import datetime
from sqlalchemy import func

class Permission(db.Model):
    """权限表 - 定义系统中的所有权限"""
    __tablename__ = 'permissions'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(100), nullable=False, unique=True)  # 权限名称，如 'dbsync.sql_sync'
    description = db.Column(db.String(255), nullable=True)  # 权限描述
    module = db.Column(db.String(50), nullable=False)  # 模块名称，如 'dbsync'
    function_name = db.Column(db.String(100), nullable=False)  # 函数名称，如 'sql_sync'
    created_at = db.Column(db.DateTime, nullable=False, default=func.current_timestamp())
    updated_at = db.Column(db.DateTime, nullable=False, default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'module': self.module,
            'function_name': self.function_name,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

class UserPermission(db.Model):
    """用户权限关联表"""
    __tablename__ = 'user_permissions'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    permission_id = db.Column(db.Integer, db.ForeignKey('permissions.id'), nullable=False)
    granted_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)  # 授权人
    granted_at = db.Column(db.DateTime, nullable=False, default=func.current_timestamp())
    is_active = db.Column(db.Boolean, default=True)  # 权限是否激活
    
    # 建立关系
    user = db.relationship('User', foreign_keys=[user_id], backref='user_permissions')
    permission = db.relationship('Permission', backref='user_permissions')
    granter = db.relationship('User', foreign_keys=[granted_by])
    
    # 联合唯一约束
    __table_args__ = (db.UniqueConstraint('user_id', 'permission_id', name='uk_user_permission'),)
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'permission_id': self.permission_id,
            'granted_by': self.granted_by,
            'granted_at': self.granted_at.strftime('%Y-%m-%d %H:%M:%S') if self.granted_at else None,
            'is_active': self.is_active,
            'permission': self.permission.to_dict() if self.permission else None,
            'granter_name': self.granter.username if self.granter else None
        }

class OperationLog(db.Model):
    """操作日志表"""
    __tablename__ = 'operation_logs'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    permission_name = db.Column(db.String(100), nullable=False)  # 权限名称
    module = db.Column(db.String(50), nullable=False)  # 模块名称
    function_name = db.Column(db.String(100), nullable=False)  # 函数名称
    request_method = db.Column(db.String(10), nullable=False)  # HTTP方法
    request_path = db.Column(db.String(255), nullable=False)  # 请求路径
    request_params = db.Column(db.Text, nullable=True)  # 请求参数（JSON格式）
    ip_address = db.Column(db.String(45), nullable=True)  # 客户端IP
    user_agent = db.Column(db.String(500), nullable=True)  # 用户代理
    status_code = db.Column(db.Integer, nullable=True)  # 响应状态码
    response_message = db.Column(db.Text, nullable=True)  # 响应消息
    execution_time = db.Column(db.Float, nullable=True)  # 执行时间（秒）
    created_at = db.Column(db.DateTime, nullable=False, default=func.current_timestamp())
    
    # 建立关系
    user = db.relationship('User', backref='operation_logs')
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            'permission_name': self.permission_name,
            'module': self.module,
            'function_name': self.function_name,
            'request_method': self.request_method,
            'request_path': self.request_path,
            'request_params': self.request_params,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'status_code': self.status_code,
            'response_message': self.response_message,
            'execution_time': self.execution_time,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }
