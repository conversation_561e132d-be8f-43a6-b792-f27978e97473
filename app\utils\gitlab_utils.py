"""
GitLab API工具模块
用于检查GitLab合并请求状态
"""

import requests
import re
import logging
from datetime import datetime, timezone, timedelta
from urllib.parse import urlparse, parse_qs

# 配置日志
logger = logging.getLogger(__name__)

# 北京时区 (UTC+8)
BEIJING_TZ = timezone(timedelta(hours=8))

def convert_utc_to_beijing_time(utc_time_str):
    """
    将UTC时间字符串转换为北京时间

    Args:
        utc_time_str (str): UTC时间字符串，格式如 "2025-06-17T02:02:10.245Z"

    Returns:
        str: 北京时间字符串，格式如 "2025-06-17 10:02:10"
    """
    if not utc_time_str:
        return ""

    try:
        # 解析UTC时间
        if utc_time_str.endswith('Z'):
            utc_time_str = utc_time_str[:-1] + '+00:00'

        # 解析时间字符串
        if '.' in utc_time_str:
            # 包含毫秒的格式
            dt = datetime.fromisoformat(utc_time_str.replace('Z', '+00:00'))
        else:
            # 不包含毫秒的格式
            dt = datetime.fromisoformat(utc_time_str)

        # 确保时间是UTC时区
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)

        # 转换为北京时间
        beijing_time = dt.astimezone(BEIJING_TZ)

        # 返回格式化的时间字符串
        return beijing_time.strftime('%Y-%m-%d %H:%M:%S')

    except Exception as e:
        logger.warning(f"时间转换失败: {utc_time_str}, 错误: {str(e)}")
        return utc_time_str  # 转换失败时返回原始时间

# 导入配置
try:
    from config import GITLAB_CONFIG
except ImportError:
    logger.warning("无法导入GitLab配置，将使用默认配置")
    GITLAB_CONFIG = {
        'base_url': 'http://***********:58880',
        'api_base_url': 'http://***********:58880/api/v4',
        'api_token': '',
        'address_mapping': {
            'internal_address': '**************',
            'external_address': '***********:58880',
            'internal_base_url': 'http://**************',
            'external_base_url': 'http://***********:58880'
        }
    }


class GitLabMergeRequestChecker:
    """GitLab合并请求状态检查器"""

    def __init__(self, api_token=None, debug=False):
        """
        初始化GitLab检查器

        Args:
            api_token (str): GitLab API Token
            debug (bool): 是否启用调试模式
        """
        self.api_token = api_token or GITLAB_CONFIG.get('api_token', '')
        self.base_url = GITLAB_CONFIG.get('base_url', 'http://***********:58880')
        self.api_base_url = GITLAB_CONFIG.get('api_base_url', 'http://***********:58880/api/v4')
        self.debug = debug

        # 获取地址映射配置
        self.address_mapping = GITLAB_CONFIG.get('address_mapping', {})

        # 设置请求会话
        self.session = requests.Session()
        if self.api_token:
            self.session.headers.update({
                'Authorization': f'Bearer {self.api_token}',
                'Content-Type': 'application/json'
            })

        # 设置超时
        self.session.timeout = 30

        self._debug_print(f"GitLab检查器初始化完成，API地址: {self.api_base_url}")
        self._debug_print(f"地址映射配置: {self.address_mapping}")
    
    def _debug_print(self, message):
        """调试输出"""
        if self.debug:
            logger.debug(f"[GitLab DEBUG] {message}")

    def convert_internal_to_external_url(self, url):
        """
        将内网地址转换为外网地址

        Args:
            url (str): 原始URL（可能是内网地址）

        Returns:
            str: 转换后的URL（外网地址）
        """
        if not self.address_mapping:
            return url

        internal_address = self.address_mapping.get('internal_address', '')
        external_address = self.address_mapping.get('external_address', '')

        if internal_address and external_address and internal_address in url:
            converted_url = url.replace(internal_address, external_address)
            self._debug_print(f"URL地址转换: {url} -> {converted_url}")
            return converted_url

        return url
    
    def test_api_connection(self):
        """
        测试GitLab API连接
        
        Returns:
            bool: 连接是否成功
        """
        if not self.api_token:
            logger.error("未设置GitLab API Token")
            return False
        
        try:
            # 测试获取当前用户信息
            test_url = f"{self.api_base_url}/user"
            response = self.session.get(test_url)
            
            if response.status_code == 200:
                user_info = response.json()
                logger.info(f"GitLab API连接成功，当前用户: {user_info.get('username', 'unknown')}")
                return True
            elif response.status_code == 401:
                logger.error("GitLab API Token无效或已过期")
                return False
            elif response.status_code == 403:
                logger.error("GitLab API Token权限不足")
                return False
            else:
                logger.error(f"GitLab API连接失败，状态码: {response.status_code}")
                return False
        
        except Exception as e:
            logger.error(f"测试GitLab API连接时发生异常: {str(e)}")
            return False
    
    def parse_merge_request_url(self, url):
        """
        解析GitLab合并请求URL，提取项目ID和合并请求ID
        支持内网和外网地址格式

        Args:
            url (str): GitLab合并请求URL

        Returns:
            dict: 解析结果，包含project_id和merge_request_iid
        """
        try:
            # 支持的地址格式：
            # 内网: http://**************/project/path/-/merge_requests/123
            # 外网: http://***********:58880/project/path/-/merge_requests/123

            # 构建匹配模式，支持两种地址
            internal_pattern = r'^https?://192\.168\.40\.191/([^/]+(?:/[^/]+)*?)(?:/-)?/merge_requests/(\d+)'
            external_pattern = r'^https?://43\.254\.89\.5:58880/([^/]+(?:/[^/]+)*?)(?:/-)?/merge_requests/(\d+)'

            # 尝试匹配内网地址
            match = re.match(internal_pattern, url)
            if not match:
                # 尝试匹配外网地址
                match = re.match(external_pattern, url)

            if match:
                project_path = match.group(1)
                merge_request_iid = int(match.group(2))

                self._debug_print(f"解析URL成功: project_path={project_path}, merge_request_iid={merge_request_iid}")

                return {
                    'success': True,
                    'project_path': project_path,
                    'merge_request_iid': merge_request_iid,
                    'url': url,
                    'original_url': url
                }
            else:
                self._debug_print(f"URL格式不匹配: {url}")
                return {
                    'success': False,
                    'error': 'URL格式不匹配，支持的格式: http://**************/... 或 http://***********:58880/...',
                    'url': url
                }

        except Exception as e:
            self._debug_print(f"解析URL异常: {str(e)}")
            return {
                'success': False,
                'error': f'解析URL异常: {str(e)}',
                'url': url
            }
    
    def get_project_id_by_path(self, project_path):
        """
        根据项目路径获取项目ID
        
        Args:
            project_path (str): 项目路径
            
        Returns:
            int: 项目ID，失败返回None
        """
        try:
            # URL编码项目路径
            encoded_path = requests.utils.quote(project_path, safe='')
            url = f"{self.api_base_url}/projects/{encoded_path}"
            
            response = self.session.get(url)
            
            if response.status_code == 200:
                project_info = response.json()
                project_id = project_info.get('id')
                self._debug_print(f"获取项目ID成功: {project_path} -> {project_id}")
                return project_id
            else:
                self._debug_print(f"获取项目ID失败，状态码: {response.status_code}")
                return None
        
        except Exception as e:
            self._debug_print(f"获取项目ID异常: {str(e)}")
            return None
    
    def get_merge_request_status(self, project_id, merge_request_iid):
        """
        获取合并请求状态
        
        Args:
            project_id (int): 项目ID
            merge_request_iid (int): 合并请求IID
            
        Returns:
            dict: 合并请求信息
        """
        try:
            url = f"{self.api_base_url}/projects/{project_id}/merge_requests/{merge_request_iid}"
            response = self.session.get(url)
            
            if response.status_code == 200:
                mr_info = response.json()
                
                # 提取关键信息
                state = mr_info.get('state', 'unknown')
                title = mr_info.get('title', '')
                author = mr_info.get('author', {}).get('name', 'unknown')
                source_branch = mr_info.get('source_branch', '')
                target_branch = mr_info.get('target_branch', '')

                # 时间转换为北京时间
                created_at_utc = mr_info.get('created_at', '')
                updated_at_utc = mr_info.get('updated_at', '')
                merged_at_utc = mr_info.get('merged_at', '')

                created_at = convert_utc_to_beijing_time(created_at_utc)
                updated_at = convert_utc_to_beijing_time(updated_at_utc)
                merged_at = convert_utc_to_beijing_time(merged_at_utc) if merged_at_utc else ''

                web_url = mr_info.get('web_url', '')
                
                # 状态映射
                status_mapping = {
                    'opened': '开放中',
                    'merged': '已合并',
                    'closed': '已关闭'
                }
                
                status_text = status_mapping.get(state, state)
                
                self._debug_print(f"获取合并请求信息成功: {title} - {status_text}")
                
                return {
                    'success': True,
                    'state': state,
                    'status': status_text,
                    'title': title,
                    'author': author,
                    'source_branch': source_branch,
                    'target_branch': target_branch,
                    'created_at': created_at,  # 北京时间
                    'updated_at': updated_at,  # 北京时间
                    'merged_at': merged_at,    # 北京时间
                    'created_at_utc': created_at_utc,  # 原始UTC时间
                    'updated_at_utc': updated_at_utc,  # 原始UTC时间
                    'merged_at_utc': merged_at_utc,    # 原始UTC时间
                    'web_url': web_url,
                    'timezone': 'Asia/Shanghai (UTC+8)',  # 时区说明
                    'raw_info': mr_info
                }
            
            elif response.status_code == 404:
                self._debug_print("合并请求不存在")
                return {
                    'success': False,
                    'error': '合并请求不存在',
                    'status_code': 404
                }
            
            else:
                self._debug_print(f"获取合并请求信息失败，状态码: {response.status_code}")
                return {
                    'success': False,
                    'error': f'API请求失败，状态码: {response.status_code}',
                    'status_code': response.status_code
                }
        
        except Exception as e:
            self._debug_print(f"获取合并请求信息异常: {str(e)}")
            return {
                'success': False,
                'error': f'获取合并请求信息异常: {str(e)}'
            }
    
    def check_merge_request_status(self, url):
        """
        检查指定URL的合并请求状态
        
        Args:
            url (str): GitLab合并请求URL
            
        Returns:
            dict: 检查结果
        """
        try:
            # 解析URL
            parse_result = self.parse_merge_request_url(url)
            if not parse_result['success']:
                return {
                    'success': False,
                    'url': url,
                    'error': parse_result['error'],
                    'status': '解析失败',
                    'description': 'URL解析失败',
                    'status_code': 'PARSE_FAILED'
                }
            
            project_path = parse_result['project_path']
            merge_request_iid = parse_result['merge_request_iid']
            
            # 获取项目ID
            project_id = self.get_project_id_by_path(project_path)
            if not project_id:
                return {
                    'success': False,
                    'url': url,
                    'error': '无法获取项目ID',
                    'status': '项目不存在',
                    'description': '无法找到指定的项目',
                    'status_code': 'PROJECT_NOT_FOUND'
                }
            
            # 获取合并请求状态
            mr_result = self.get_merge_request_status(project_id, merge_request_iid)
            if not mr_result['success']:
                return {
                    'success': False,
                    'url': url,
                    'error': mr_result['error'],
                    'status': '获取失败',
                    'description': '无法获取合并请求状态',
                    'status_code': 'MR_NOT_FOUND'
                }
            
            # 构建返回结果
            state = mr_result['state']
            status_code_mapping = {
                'opened': 'MR_OPENED',
                'merged': 'MR_MERGED',
                'closed': 'MR_CLOSED'
            }
            
            description_mapping = {
                'opened': '合并请求正在等待审核或合并',
                'merged': '合并请求已成功合并',
                'closed': '合并请求已关闭（未合并）'
            }
            
            # 转换URL为外网地址（如果需要）
            external_url = self.convert_internal_to_external_url(url)

            # 获取当前北京时间
            beijing_now = datetime.now(BEIJING_TZ)

            return {
                'success': True,
                'url': url,
                'external_url': external_url,
                'status': mr_result['status'],
                'description': description_mapping.get(state, '合并请求状态未知'),
                'status_code': status_code_mapping.get(state, 'MR_UNKNOWN'),
                'title': mr_result['title'],
                'author': mr_result['author'],
                'source_branch': mr_result['source_branch'],
                'target_branch': mr_result['target_branch'],
                'created_at': mr_result['created_at'],      # 北京时间
                'updated_at': mr_result['updated_at'],      # 北京时间
                'merged_at': mr_result['merged_at'],        # 北京时间
                'created_at_utc': mr_result.get('created_at_utc', ''),  # 原始UTC时间
                'updated_at_utc': mr_result.get('updated_at_utc', ''),  # 原始UTC时间
                'merged_at_utc': mr_result.get('merged_at_utc', ''),    # 原始UTC时间
                'project_path': project_path,
                'merge_request_iid': merge_request_iid,
                'timezone': 'Asia/Shanghai (UTC+8)',        # 时区说明
                'check_time': beijing_now.strftime('%Y-%m-%d %H:%M:%S')  # 检查时间（北京时间）
            }
        
        except Exception as e:
            logger.error(f"检查合并请求状态异常: {url}, 错误: {str(e)}")
            return {
                'success': False,
                'url': url,
                'error': f'检查异常: {str(e)}',
                'status': '检查失败',
                'description': '状态检查过程中出现异常',
                'status_code': 'CHECK_FAILED'
            }


def check_gitlab_merge_request_status(url, debug=False, api_token=None):
    """
    检查单个GitLab合并请求状态的便捷函数
    
    Args:
        url (str): GitLab合并请求URL
        debug (bool): 是否启用调试模式
        api_token (str): GitLab API Token (可选，默认使用配置文件中的token)
        
    Returns:
        dict: 检查结果
    """
    checker = GitLabMergeRequestChecker(api_token=api_token, debug=debug)
    
    # 检查是否有可用的token
    if not checker.api_token:
        return {
            'success': False,
            'url': url,
            'error': '未提供GitLab API Token且配置文件中未设置默认token',
            'status': 'Token缺失',
            'description': '无法进行API调用',
            'status_code': 'TOKEN_MISSING'
        }
    
    # 测试API连接
    if not checker.test_api_connection():
        return {
            'success': False,
            'url': url,
            'error': 'GitLab API连接失败',
            'status': 'API连接失败',
            'description': '无法连接到GitLab API',
            'status_code': 'API_CONNECTION_FAILED'
        }
    
    # 检查合并请求状态
    return checker.check_merge_request_status(url)
