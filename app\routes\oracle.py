from flask import Blueprint, jsonify, request, current_app
from flask_jwt_extended import jwt_required
from app.models.oracle_server import OracleServer
from app.utils.oracle_utils import (
    get_tablespace_info, 
    extend_tablespace, 
    get_disk_space
)
from app.utils.auth_utils import admin_required
from app import db

oracle_bp = Blueprint('oracle', __name__)

@oracle_bp.route('/oracle/servers', methods=['GET'])
@jwt_required()
def get_servers():
    """
    获取所有Oracle服务器列表
    """
    try:
        servers = OracleServer.query.filter_by(status=True).all()
        return jsonify({
            'success': True,
            'message': '获取服务器列表成功',
            'data': [server.to_dict() for server in servers],
            'code': 200
        })
    except Exception as e:
        current_app.logger.error(f"获取服务器列表出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'系统错误: {str(e)}',
            'code': 500
        }), 500

@oracle_bp.route('/oracle/servers', methods=['POST'])
@jwt_required()
@admin_required
def add_server():
    """
    添加新的Oracle服务器
    """
    data = request.json
    if not data or not data.get('ssh_address') or not data.get('server_name'):
        return jsonify({
            'success': False,
            'message': '请提供服务器IP地址和名称',
            'code': 400
        }), 400
    
    try:
        # 检查IP是否已存在
        existing_server = OracleServer.query.filter_by(ssh_address=data['ssh_address']).first()
        if existing_server:
            return jsonify({
                'success': False,
                'message': f'服务器IP {data["ssh_address"]} 已存在',
                'code': 400
            }), 400
        
        # 创建新服务器记录
        new_server = OracleServer(
            ssh_address=data['ssh_address'],
            server_name=data['server_name'],
            db_address=data['ssh_address'],
            db_port=data.get('db_port', 1521),
            ssh_port=data.get('ssh_port', 22),
            ssh_username=data.get('ssh_username', 'oracle'),
            status=data.get('status', True),
            db_username=data.get('db_username', 'paymentdb'),
            db_password=data.get('db_password', 'paymentdb2019@ORCL')
        )
        
        db.session.add(new_server)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '添加服务器成功',
            'data': new_server.to_dict(),
            'code': 201
        }), 201
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"添加服务器出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'系统错误: {str(e)}',
            'code': 500
        }), 500

@oracle_bp.route('/oracle/servers/<int:server_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_server(server_id):
    """
    更新Oracle服务器信息
    """
    data = request.json
    if not data:
        return jsonify({
            'success': False,
            'message': '请提供更新数据',
            'code': 400
        }), 400
    
    try:
        server = OracleServer.query.get(server_id)
        if not server:
            return jsonify({
                'success': False,
                'message': f'服务器ID {server_id} 不存在',
                'code': 404
            }), 404
        
        # 更新服务器信息
        if 'server_name' in data:
            server.server_name = data['server_name']
        if 'ssh_address' in data:
            server.ssh_address = data['ssh_address']
            # 默认db_address与ssh_address相同
            server.db_address = data['ssh_address']
        if 'ssh_port' in data:
            server.ssh_port = data['ssh_port']
        if 'db_port' in data:
            server.db_port = data['db_port']
        if 'ssh_username' in data:
            server.ssh_username = data['ssh_username']
        if 'status' in data:
            server.status = data['status']
        if 'db_username' in data:
            server.db_username = data['db_username']
        if 'db_password' in data:
            server.db_password = data['db_password']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '更新服务器成功',
            'data': server.to_dict(),
            'code': 200
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新服务器出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'系统错误: {str(e)}',
            'code': 500
        }), 500

@oracle_bp.route('/oracle/servers/<int:server_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_server(server_id):
    """
    删除Oracle服务器
    """
    try:
        server = OracleServer.query.get(server_id)
        if not server:
            return jsonify({
                'success': False,
                'message': f'服务器ID {server_id} 不存在',
                'code': 404
            }), 404
        
        db.session.delete(server)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '删除服务器成功',
            'code': 200
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除服务器出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'系统错误: {str(e)}',
            'code': 500
        }), 500

@oracle_bp.route('/oracle/tablespace', methods=['GET'])
@jwt_required()
def get_tablespace():
    """
    获取Oracle表空间信息和数据文件信息
    """
    server_id = request.args.get('server_id')
    if not server_id:
        return jsonify({
            'success': False,
            'message': '请提供服务器ID',
            'code': 400
        }), 400
    
    try:
        server = OracleServer.query.get(server_id)
        if not server:
            return jsonify({
                'success': False,
                'message': f'服务器ID {server_id} 不存在',
                'code': 404
            }), 404
        
        success, results = get_tablespace_info(server_id)
        if success:
            current_app.logger.info(f"获取表空间和数据文件信息成功: {results}")
            if len(results) > 0:
                # 获取第一条记录的表空间基本信息
                first_record = results[0]
                tablespace_info = {
                    'tablespace_name': first_record.get('tablespace_name'),
                    'total_size_mb': first_record.get('total_size_mb'),
                    'used_size_mb': first_record.get('used_size_mb'),
                    'free_size_mb': first_record.get('free_size_mb'),
                    'free_percent': first_record.get('free_percent'),
                    'datafiles': [{
                        'file_name': df.get('file_name'),
                        'size_mb': df.get('size_mb'),
                        'max_size_mb': df.get('max_size_mb'),
                        'auto_extend': df.get('auto_extend')
                    } for df in results]
                }                
            return jsonify({
                'success': True,
                'message': '获取表空间和数据文件信息成功',
                'data': {
                    'server': server.to_dict(),
                    'tablespace_info': tablespace_info
                },
                'code': 200
            })
        else:
            return jsonify({
                'success': False,
                'message': f'获取表空间和数据文件信息失败: {result}',
                'code': 500
            }), 500
    except Exception as e:
        current_app.logger.error(f"获取表空间和数据文件信息出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'系统错误: {str(e)}',
            'code': 500
        }), 500

@oracle_bp.route('/oracle/extend_tablespace', methods=['POST'])
@jwt_required()
@admin_required
def extend_tablespace_api():
    """
    扩容Oracle表空间
    """
    data = request.json
    if not data or not data.get('server_id') or not data.get('tablespace_name'):
        return jsonify({
            'success': False,
            'message': '请提供服务器ID和表空间名称',
            'code': 400
        }), 400
    
    size_mb = data.get('size_mb', 1024) 
    datafile_name = data.get('datafile_name')
    server_id = data.get('server_id') 
    tablespace_name = data.get('tablespace_name')
    
    try:
        server = OracleServer.query.get(data['server_id'])
        if not server:
            return jsonify({
                'success': False,
                'message': f'服务器ID {data["server_id"]} 不存在',
                'code': 404
            }), 404
        
        success, result = extend_tablespace(server_id, tablespace_name, datafile_name, size_mb)
        if success:
            return jsonify({
                'success': True,
                'message': '表空间扩容成功',
                'data': None,
                'code': 200
            })
        else:
            return jsonify({
                'success': False,
                'message': f'扩容表空间失败: {result}',
                'code': 500
            }), 500
    except Exception as e:
        current_app.logger.error(f"扩容表空间出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'系统错误: {str(e)}',
            'code': 500
        }), 500

@oracle_bp.route('/oracle/disk_space', methods=['GET'])
@jwt_required()
def get_server_disk_space():
    """
    获取服务器磁盘空间信息
    """
    server_id = request.args.get('server_id')
    if not server_id:
        return jsonify({
            'success': False,
            'message': '请提供服务器ID',
            'code': 400
        }), 400
    
    try:
        server = OracleServer.query.get(server_id)
        if not server:
            return jsonify({
                'success': False,
                'message': f'服务器ID {server_id} 不存在',
                'code': 404
            }), 404
        
        success, result = get_disk_space(server.ssh_address, server.ssh_username, server.ssh_port)
        if success:
            return jsonify({
                'success': True,
                'message': '获取磁盘空间信息成功',
                'data': {
                    'server': server.to_dict(),
                    'disk_space_info': result
                },
                'code': 200
            })
        else:
            return jsonify({
                'success': False,
                'message': f'获取磁盘空间信息失败: {result}',
                'code': 500
            }), 500
    except Exception as e:
        current_app.logger.error(f"获取磁盘空间信息出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'系统错误: {str(e)}',
            'code': 500
        }), 500