from flask import Blueprint, jsonify, request, current_app
from flask_jwt_extended import jwt_required, current_user
from app.utils.wecom_utils import send_wecom_message
from app.models.nginx_check import NginxFileCheck
from app import db
import subprocess
import shlex
import json
from datetime import datetime,timedelta
from concurrent.futures import ThreadPoolExecutor
from app.utils.auth_utils import permission_required

# 定义需要检查的节点IP列表
NGINX_NODES = ['********', '********', '********', '********', '************', '************']

# 定义需要检查的目录列表
NGINX_DIRS = ['prod', 'tools', 'test', 'uat', 'sjk', 'oem', 'youfu', 'dev', 'common']

nginx_bp = Blueprint('nginx', __name__, url_prefix='/nginx')


def check_file_consistency(node_ip, file_path):
    """检查单个文件在指定节点上的内容"""
    try:
        cmd = f'ssh -o ConnectTimeout=10 root@{node_ip} "cat {file_path} 2>/dev/null || echo \'\'"'
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=15
        )
        if result.returncode == 0:
            return {
                'node': node_ip,
                'content': result.stdout,
                'status': 'success'
            }
        else:
            return {
                'node': node_ip,
                'content': '',
                'status': 'error',
                'message': result.stderr.strip() if result.stderr else "未知错误"
            }
    except Exception as e:
        current_app.logger.error(f"检查节点 {node_ip} 的文件 {file_path} 失败: {str(e)}")
        return {
            'node': node_ip,
            'content': '',
            'status': 'error',
            'message': str(e)
        }


def get_config_files(node_ip, nginx_dir):
    """获取指定节点和目录下的所有配置文件"""
    try:
        remote_cmd = f'find /etc/nginx/{nginx_dir} -type f \\( -name \"*.conf\" -o -name \"*.ini\" \\) -print'
        safe_remote_cmd = shlex.quote(remote_cmd)
        cmd = f'ssh -o ConnectTimeout=15 root@{node_ip} {safe_remote_cmd}'
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=15
        )
        if result.returncode == 0:
            files = [f.strip() for f in result.stdout.strip().split('\n') if f.strip()]
            return {
                'node': node_ip,
                'files': files,
                'status': 'success'
            }
        else:
            return {
                'node': node_ip,
                'files': [],
                'status': 'error',
                'message': result.stderr.strip() if result.stderr else "未知错误"
            }
    except Exception as e:
        current_app.logger.error(f"获取节点 {node_ip} 的目录 {nginx_dir} 下的配置文件失败: {str(e)}")
        return {
            'node': node_ip,
            'files': [],
            'status': 'error',
            'message': str(e)
        }

@nginx_bp.route('/nginx_consistency/file', methods=['POST'])
@jwt_required()
def check_single_file():
    """检查单个配置文件的一致性"""
    if not current_user:
        return jsonify({
            'success': False,
            'message': '未授权的访问',
            'code': 401
        }), 401
    
    data = request.get_json()
    current_app.logger.info(f"check_single_file获取到的请求数据: {data}")
    if not data or 'file_path' not in data:
        return jsonify({
            'success': False,
            'message': 'check_single_file缺少文件路径参数',
            'code': 400
        }), 400
    
    file_path = data['file_path']
    # 确保文件路径是绝对路径
    if not file_path.startswith('/etc/nginx/'):
        file_path = f'/etc/nginx/{file_path}'
    
    try:
        # 并行检查所有节点上的文件
        with ThreadPoolExecutor(max_workers=len(NGINX_NODES)) as executor:
            results = list(executor.map(lambda n: check_file_consistency(n, file_path), NGINX_NODES))
        
        # 检查文件内容是否一致
        reference_node = NGINX_NODES[0]
        reference_content = next((r['content'] for r in results if r['node'] == reference_node), '')
        is_consistent = True
        nodes_content = []
        
        for result in results:
            node_consistent = result['status'] == 'success' and result['content'] == reference_content
            if not node_consistent:
                is_consistent = False
            
            nodes_content.append({
                'node': result['node'],
                'consistent': node_consistent,
                'content': result['content'],
                'status': result['status'],
                'message': result.get('message', '')
            })
        
        # 将检查结果保存到数据库
        check_result = {
            'file_path': file_path,
            'consistent': is_consistent,
            'nodes': nodes_content
        }
        
        # 将结果转换为JSON字符串存储
        check_result_json = json.dumps(check_result)
        
        # 创建或更新数据库记录
        nginx_check = NginxFileCheck.query.filter_by(file_path=file_path).first()
        if nginx_check:
            nginx_check.check_time = datetime.utcnow() + timedelta(hours=8)
            nginx_check.is_consistent = is_consistent
            nginx_check.check_result = check_result_json
        else:
            nginx_check = NginxFileCheck(
                file_path=file_path,
                check_time=datetime.utcnow() + timedelta(hours=8),
                is_consistent=is_consistent,
                check_result=check_result_json
            )
            db.session.add(nginx_check)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '已完成文件一致性检查并记录结果',
            'file_path': file_path,
            'consistent': is_consistent,
            'nodes': nodes_content,
            'code': 200
        })
    
    except Exception as e:
        current_app.logger.error(f"检查文件 {file_path} 一致性失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'检查失败：{str(e)}',
            'code': 500
        }), 500


@nginx_bp.route('/nginx_directories', methods=['GET'])
@jwt_required()
def get_nginx_directories():
    """获取Nginx配置目录列表"""
    if not current_user:
        return jsonify({
            'success': False,
            'message': '未授权的访问',
            'code': 401
        }), 401
    
    try:
        return jsonify({
            'success': True,
            'message': '已获取Nginx配置目录列表',
            'directories': NGINX_DIRS,
            'code': 200
        })
    except Exception as e:
        current_app.logger.error(f"获取Nginx配置目录列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取失败：{str(e)}',
            'code': 500
        }), 500


@nginx_bp.route('/nginx_files', methods=['POST'])
@jwt_required()
def get_directory_files():
    """获取指定目录下的配置文件列表"""
    if not current_user:
        return jsonify({
            'success': False,
            'message': '未授权的访问',
            'code': 401
        }), 401
    
    data = request.get_json()
    if not data or 'directory' not in data:
        return jsonify({
            'success': False,
            'message': '缺少目录参数',
            'code': 400
        }), 400
    
    directory = data['directory']
    if directory not in NGINX_DIRS:
        return jsonify({
            'success': False,
            'message': f'无效的目录: {directory}',
            'code': 400
        }), 400
    
    try:
        # 获取参考节点上的文件列表
        reference_node = NGINX_NODES[0]
        result = get_config_files(reference_node, directory)
        
        if result['status'] == 'success':
            # 处理文件路径，只保留相对路径
            files = [f.replace(f'/etc/nginx/{directory}/', '') for f in result['files']]
            return jsonify({
                'success': True,
                'message': f'已获取目录 {directory} 下的配置文件列表',
                'directory': directory,
                'files': files,
                'code': 200
            })
        else:
            return jsonify({
                'success': False,
                'message': f'获取文件列表失败: {result.get("message", "未知错误")}',
                'code': 500
            }), 500
    
    except Exception as e:
        current_app.logger.error(f"获取目录 {directory} 下的配置文件列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取失败：{str(e)}',
            'code': 500
        }), 500


@nginx_bp.route('/access-control', methods=['POST'])
@jwt_required()
@permission_required('nginx.nginx_access_control_api')
def nginx_access_control_api():
    if not current_user:
        return jsonify({
            'success': False,
            'message': '未授权的访问',
            'code': 401
        }), 401

    data = request.get_json()
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据',
            'code': 400
        }), 400
    current_app.logger.info(f"nginx_access_control_api方法获取到的请求数据: {data}")
    action = data.get('action')
    domain = data.get('domain')

    if action not in ['allow', 'deny', 'status']:
        return jsonify({
            'success': False,
            'message': '无效的操作',
            'code': 400
        }), 400

    try:
        if action == 'status':
            # 获取所有域名的状态
            domains_status = []
            domain_list = [
                "sql.serviceshare.com",
                "wiki.serviceshare.com",
                "zabbix.serviceshare.com",
                "nacos.serviceshare.com",
                "jenkinsdl.serviceshare.com",
                "harbor.serviceshare.com",
                "dashboard.serviceshare.com",
                "apollo.serviceshare.com",
                "tidbdashboard.serviceshare.com",
                "nightingale.serviceshare.com",
                "jk.serviceshare.com",
                "rabbitmq.serviceshare.com",
                "js.serviceshare.com",
                "kibana.serviceshare.com"
            ]

            try:
                domains_data = {}
                for d in domain_list:
                    # 检查配置文件中是否包含deny规则，以及是否被注释
                    check_cmd = f'ssh -o ConnectTimeout=20 root@******** "grep deny /etc/nginx/tools/{d}.conf || echo \'\'"'
                    result = subprocess.run(
                        check_cmd,
                        shell=True,
                        capture_output=True,
                        text=True,
                        timeout=10
                    )

                    # 根据原脚本逻辑判断状态
                    output = result.stdout.strip()
                    if not output:
                        domains_data[d] = "unknown"  # 未找到deny语句
                    elif "#deny" in output:
                        domains_data[d] = "allow"  # 找到被注释的deny语句
                    else:
                        domains_data[d] = "deny"  # 找到未被注释的deny语句

                # 构建状态列表
                for d in domain_list:
                    status = domains_data.get(d, "unknown")
                    domains_status.append({"name": d, "status": status})
            except Exception as e:
                current_app.logger.error(f"获取域名状态失败: {str(e)}")
                # 出错时所有域名状态未知
                for d in domain_list:
                    domains_status.append({"name": d, "status": "unknown"})

            return jsonify({
                'success': True,
                'message': '已获取所有域名状态',
                'domains': domains_status,
                'code': 200
            })
        else:
            # 处理单个域名的允许/拒绝操作
            if not domain:
                return jsonify({
                    'success': False,
                    'message': '缺少域名参数',
                    'code': 400
                }), 400

            # 构建要执行的命令，使用正则表达式处理多个空格的情况
            if action == "allow":
                # 先确保所有#deny all变成deny all，然后再将deny all变成#deny all
                cmd = f'ssh -o ConnectTimeout=20 root@******** "sed -i \'s/#\\s*deny\\s\\+all/deny all/g\' /etc/nginx/tools/{domain}.conf && sed -i \'s/deny\\s\\+all/#deny all/g\' /etc/nginx/tools/{domain}.conf && nohup /etc/nginx/transfile.sh {domain}.conf tools > /dev/null 2>&1 &"'
            else:  # deny
                # 确保所有#deny all变成deny all
                cmd = f'ssh -o ConnectTimeout=20 root@******** "sed -i \'s/#\\s*deny\\s\\+all/deny all/g\' /etc/nginx/tools/{domain}.conf && nohup /etc/nginx/transfile.sh {domain}.conf tools > /dev/null 2>&1 &"'

            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                # 发送企业微信通知 (注释掉，因为项目中没有实现该函数)
                action_text = "允许" if action == "allow" else "拒绝"
                send_wecom_message(f"修改域名 {domain} 的外网访问控制操作：{action_text}外网访问")

                return jsonify({
                    'success': True,
                    'message': f'已{action_text}域名{domain}的外网访问',
                    'code': 200
                })
            else:
                error_msg = result.stderr.strip() if result.stderr else "未知错误"
                return jsonify({
                    'success': False,
                    'message': f'操作失败：{error_msg}',
                    'code': 500
                }), 500

    except Exception as e:
        current_app.logger.error(f"Nginx访问控制操作失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'操作失败：{str(e)}',
            'code': 500
        }), 500


@nginx_bp.route('/nginx_consistency/compare', methods=['POST'])
@jwt_required()
def compare_multiple_files():
    """比较多个配置文件的一致性，立即返回响应，后台异步执行"""
    if not current_user:
        return jsonify({
            'success': False,
            'message': '未授权的访问',
            'code': 401
        }), 401
    
    data = request.get_json()
    current_app.logger.info(f"compare_multiple_files获取到的请求数据: {data}")
    if not data or 'file_paths' not in data or not isinstance(data['file_paths'], list) or not data['file_paths']:
        return jsonify({
            'success': False,
            'message': '缺少文件路径参数或格式不正确',
            'code': 400
        }), 400
    
    file_paths = data['file_paths']
    
    # 获取当前应用上下文，以便在线程中使用
    app = current_app._get_current_object()
    
    # 立即返回响应，告知前端请求已接收
    # 启动后台线程执行文件一致性检查
    executor = ThreadPoolExecutor(max_workers=1)
    executor.submit(process_files_consistency_check, app, file_paths)
    
    return jsonify({
        'success': True,
        'message': '已接收文件一致性检查请求，正在后台处理中，请稍后查询结果',
        'file_paths': file_paths,
        'code': 200
    })


def process_files_consistency_check(app, file_paths):
    """后台处理多个文件的一致性检查"""
    # 使用应用上下文，确保在线程中可以访问current_app和数据库
    with app.app_context():
        try:
            for file_path in file_paths:
                # 确保文件路径是绝对路径
                if not file_path.startswith('/etc/nginx/'):
                    file_path = f'/etc/nginx/{file_path}'
                app.logger.info(f"process_files_consistency_check方法获取到的文件路径: {file_path}")
                # 并行检查所有节点上的文件
                with ThreadPoolExecutor(max_workers=len(NGINX_NODES)) as executor:
                    results = list(executor.map(lambda n: check_file_consistency(n, file_path), NGINX_NODES))
                # 检查文件内容是否一致
                reference_node = NGINX_NODES[0]
                reference_content = next((r['content'] for r in results if r['node'] == reference_node), '')
                is_consistent = True
                nodes_content = []
                
                for result in results:
                    node_consistent = result['status'] == 'success' and result['content'] == reference_content
                    if not node_consistent:
                        is_consistent = False
                    
                    nodes_content.append({
                        'node': result['node'],
                        'consistent': node_consistent,
                        'content': result['content'],
                        'status': result['status'],
                        'message': result.get('message', '')
                    })
                
                # 将检查结果保存到数据库
                check_result = {
                    'file_path': file_path,
                    'consistent': is_consistent,
                    'nodes': nodes_content
                }

                check_result_json = json.dumps(check_result)
                # 创建或更新数据库记录
                nginx_check = NginxFileCheck.query.filter_by(file_path=file_path).first()
                if nginx_check:
                    nginx_check.check_time = datetime.utcnow() + timedelta(hours=8)
                    nginx_check.is_consistent = is_consistent
                    nginx_check.check_result = check_result_json
                else:
                    nginx_check = NginxFileCheck(
                        file_path=file_path,
                        check_time=datetime.utcnow() + timedelta(hours=8),
                        is_consistent=is_consistent,
                        check_result=check_result_json
                    )
                    db.session.add(nginx_check)
                
                db.session.commit()
                app.logger.info(f"完成文件 {file_path} 的一致性检查并保存结果")
        
        except Exception as e:
            app.logger.error(f"后台处理文件一致性检查失败: {str(e)}")


@nginx_bp.route('/nginx_consistency/file_content', methods=['POST'])
@jwt_required()
def get_file_content():
    """获取不同Nginx节点上相同配置文件的内容，用于前端差异对比显示"""
    if not current_user:
        return jsonify({
            'success': False,
            'message': '未授权的访问',
            'code': 401
        }), 401
    
    data = request.get_json()
    current_app.logger.info(f"get_file_content获取到的请求数据: {data}")
    if not data or 'file_path' not in data or not data['file_path']:
        return jsonify({
            'success': False,
            'message': '文件路径不能为空',
            'code': 400
        }), 400
    
    file_path = data['file_path']
    # 确保文件路径是绝对路径
    if not file_path.startswith('/etc/nginx/'):
        file_path = f'/etc/nginx/{file_path}'
    
    try:
        # 并行获取所有节点上的文件内容
        with ThreadPoolExecutor(max_workers=len(NGINX_NODES)) as executor:
            results = list(executor.map(lambda n: check_file_consistency(n, file_path), NGINX_NODES))
        
        # 格式化返回结果
        nodes_content = []
        for result in results:
            node_data = {
                'node': result['node'],
                'content': result['content']
            }
            
            # 如果获取内容失败，添加错误信息
            if result['status'] != 'success':
                node_data['error'] = result.get('message', '未知错误')
            
            nodes_content.append(node_data)
        
        return jsonify({
            'success': True,
            'message': '获取文件内容成功',
            'file_path': file_path,
            'nodes': nodes_content,
            'code': 200
        })
    
    except Exception as e:
        current_app.logger.error(f"获取文件 {file_path} 内容失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取文件内容失败：{str(e)}',
            'code': 500
        }), 500


@nginx_bp.route('/nginx_consistency/history', methods=['POST'])
@jwt_required()
def get_file_check_history():
    """获取配置文件的历史检查记录，支持查询多个文件"""
    if not current_user:
        return jsonify({
            'success': False,
            'message': '未授权的访问',
            'code': 401
        }), 401
    
    data = request.get_json()
    if not data:
        return jsonify({
            'success': False,
            'message': '无效的请求数据',
            'code': 400
        }), 400
    
    # 支持单个文件路径或文件路径列表
    file_paths = []
    if 'file_path' in data:
        file_paths = [data['file_path']]
    elif 'file_paths' in data and isinstance(data['file_paths'], list):
        file_paths = data['file_paths']
    
    if not file_paths:
        return jsonify({
            'success': False,
            'message': '缺少文件路径参数',
            'code': 400
        }), 400
    
    try:
        results = []
        not_found_files = []
        
        for file_path in file_paths:
            # 确保文件路径是绝对路径
            if not file_path.startswith('/etc/nginx/'):
                file_path = f'/etc/nginx/{file_path}'
            
            # 查询数据库中的最新记录
            nginx_check = NginxFileCheck.query.filter_by(file_path=file_path).order_by(NginxFileCheck.check_time.desc()).first()
            
            if not nginx_check:
                not_found_files.append(file_path)
                continue
            
            # 解析存储的JSON结果
            check_result = json.loads(nginx_check.check_result)
            
            results.append({
                'file_path': file_path,
                'consistent': nginx_check.is_consistent,
                'check_time': nginx_check.check_time.strftime('%Y-%m-%d %H:%M:%S'),
                'nodes': check_result.get('nodes', [])
            })
        
        if not results and not_found_files:
            return jsonify({
                'success': False,
                'message': '未找到任何文件的历史检查记录',
                'not_found_files': not_found_files,
                'code': 404
            }), 404
        
        return jsonify({
            'success': True,
            'message': '已获取历史检查记录',
            'results': results,
            'not_found_files': not_found_files,
            'code': 200
        })
    
    except Exception as e:
        current_app.logger.error(f"获取文件历史检查记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取历史记录失败：{str(e)}',
            'code': 500
        }), 500