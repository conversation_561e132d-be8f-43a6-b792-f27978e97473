from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, scoped_session
from config import (
    SQLALCHEMY_DATABASE_URI,
    SQLALCHEMY_POOL_RECYCLE,
    SQLALCHEMY_POOL_SIZE,
    SQLALCHEMY_POOL_TIMEOUT,
    SQLALCHEMY_MAX_OVERFLOW
)
from flask import current_app, g
from contextlib import contextmanager

# 创建数据库实例
db = SQLAlchemy()

# 创建 Base 类
Base = declarative_base()

# 创建数据库引擎，使用配置文件中的参数
engine = create_engine(
    SQLALCHEMY_DATABASE_URI,echo=False,
    pool_size=SQLALCHEMY_POOL_SIZE,
    max_overflow=SQLALCHEMY_MAX_OVERFLOW,
    pool_timeout=SQLALCHEMY_POOL_TIMEOUT,
    pool_recycle=SQLALCHEMY_POOL_RECYCLE,
    pool_pre_ping=True,
    echo_pool=False
)

Session = scoped_session(sessionmaker(bind=engine, expire_on_commit=False))

def init_db(app):
    """初始化数据库"""
    db.init_app(app)
    with app.app_context():
        db.create_all()
    
    # 注册请求结束后自动清理session的钩子
    @app.teardown_request
    def remove_session(exception=None):
        Session.remove()
        
    # 注册应用上下文结束后自动清理session的钩子
    @app.teardown_appcontext
    def shutdown_session(exception=None):
        Session.remove()

def get_db():
    """获取数据库连接"""
    return db

def get_session():
    """获取新的数据库会话"""
    return Session()

@contextmanager
def session_scope():
    """提供一个事务范围的会话上下文管理器"""
    session = get_session()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        current_app.logger.error(f"数据库会话操作出错: {str(e)}")
        raise
    finally:
        close_session(session)

def close_session(session):
    """关闭并移除数据库会话"""
    if session:
        try:
            session.close()
        except Exception as e:
            current_app.logger.error(f"关闭数据库会话时出错: {str(e)}")
        finally:
            Session.remove()

def get_user_by_id(user_id):
    """通过ID获取用户"""
    from ..models.user import User
    with session_scope() as session:
        return session.query(User).get(user_id)