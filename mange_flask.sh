#!/bin/bash

# Flask 应用的管理脚本
APP_NAME="iyunwei-api"
APP_PATH="/www/wwwroot/iyunwei-api"
APP_ENTRY="app.py"
PORT=5555

# 启动 Flask 应用
start() {
    echo "Starting $APP_NAME..."
    cd $APP_PATH || exit 1
    export LD_LIBRARY_PATH=/opt/oracle/instantclient_11_2:$LD_LIBRARY_PATH
    nohup python3 $APP_ENTRY > /dev/null 2>&1 &
    echo "$APP_NAME started on port $PORT."
}

# 关闭 Flask 应用
stop() {
    echo "Stopping $APP_NAME..."
    pkill -f "python3 $APP_ENTRY"
    echo "$APP_NAME stopped."
}

# 重启 Flask 应用
restart() {
    stop
    sleep 1
    start
}

# 查看 Flask 应用状态
status() {
    if pgrep -f "python3 $APP_ENTRY" > /dev/null; then
        echo "$APP_NAME is running."
    else
        echo "$APP_NAME is not running."
    fi
}

# 脚本使用说明
usage() {
    echo "Usage: $0 {start|stop|restart|status}"
    exit 1
}

# 主逻辑
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    *)
        usage
        ;;
esac