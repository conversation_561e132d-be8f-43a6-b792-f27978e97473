from app import db
from datetime import datetime
from sqlalchemy.sql import func

class RedisCluster(db.Model):
    __tablename__ = 'redis_clusters'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    cluster_name = db.Column(db.String(100), nullable=False)
    host = db.Column(db.String(100), nullable=False)
    port = db.Column(db.Integer, nullable=False, default=6379)
    password = db.Column(db.String(255), nullable=True)
    version = db.Column(db.String(20), nullable=True)  # 存储Redis版本，如'4.x'或'5.x'
    status = db.Column(db.<PERSON>, default=True)  # 集群状态，是否可用
    description = db.Column(db.Text, nullable=True)  # 集群描述
    is_cluster = db.Column(db.<PERSON><PERSON>, default=True)  # 是否为集群模式
    created_at = db.Column(db.DateTime, nullable=False, default=func.current_timestamp())
    updated_at = db.Column(db.DateTime, nullable=False, default=func.current_timestamp(), onupdate=func.current_timestamp())
    
    def __repr__(self):
        return f'<RedisCluster {self.cluster_name} ({self.host}:{self.port})>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'cluster_name': self.cluster_name,
            'host': self.host,
            'port': self.port,
            'password': self.password,
            'version': self.version,
            'status': self.status,
            'description': self.description,
            'is_cluster': self.is_cluster,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }