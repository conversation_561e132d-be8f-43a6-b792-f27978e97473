import redis
from flask import current_app
from redis import Redis, ConnectionPool
from rediscluster import RedisCluster as RedisClusterClient
from rediscluster import ClusterConnectionPool
from app.models.redis_cluster import RedisCluster

def get_redis_connection(cluster_id):
    """
    获取Redis连接
    :param cluster_id: Redis集群ID
    :return: Redis连接对象和错误信息
    """
    try:
        cluster = RedisCluster.query.filter_by(id=cluster_id).first()
        if not cluster:
            return None, "未找到指定ID的Redis集群配置"
                
        if cluster.is_cluster:
            # 集群模式连接
            startup_nodes = [{"host": cluster.host, "port": cluster.port}]
            pool = ClusterConnectionPool(startup_nodes=startup_nodes, 
                                        password=cluster.password if cluster.password else None, 
                                        max_connections=10, 
                                        max_connections_per_node=10)
            r = RedisClusterClient(connection_pool=pool)
        else:
            # 非集群模式连接
            connection_params = {
                "host": cluster.host,
                "port": cluster.port,
                "max_connections": 10,
            }
            
            # 如果有密码则添加密码参数
            if cluster.password:
                connection_params["password"] = cluster.password
                
            pool = ConnectionPool(**connection_params)
            r = Redis(connection_pool=pool)
        
        # 测试连接
        r.ping()
        return r, None
    except Exception as e:
        current_app.logger.error(f"Redis连接失败: {str(e)}")
        return None, f"Redis连接失败: {str(e)}"

def get_redis_info(conn):
    """
    获取Redis服务器信息
    :param conn: Redis连接对象
    :return: Redis服务器信息
    """
    try:
        info = conn.info()
        return info, None
    except Exception as e:
        current_app.logger.error(f"获取Redis信息失败: {str(e)}")
        return None, f"获取Redis信息失败: {str(e)}"

def get_redis_keys(conn, pattern='*', count=100):
    """
    获取Redis键列表
    :param conn: Redis连接对象
    :param pattern: 键匹配模式
    :param count: 返回的最大键数量
    :return: 键列表
    """
    try:
        keys = conn.keys(pattern)
        # 限制返回数量
        if len(keys) > count:
            keys = keys[:count]
        return keys, None
    except Exception as e:
        current_app.logger.error(f"获取Redis键失败: {str(e)}")
        return None, f"获取Redis键失败: {str(e)}"

# string类型
def get_redis_key_value(conn, key):
    try:
        key_type = conn.type(key).decode('utf-8')
        
        if key_type == 'string':
            value = conn.get(key).decode('utf-8')  # 添加解码
            return {'type': 'string', 'value': value}, None
        
        # list类型
        elif key_type == 'list':
            value = [item.decode('utf-8') for item in conn.lrange(key, 0, -1)]
            return {'type': 'list', 'value': value}, None
        
        # set类型
        elif key_type == 'set':
            value = [member.decode('utf-8') for member in conn.smembers(key)]
            return {'type': 'set', 'value': value}, None
        
        # zset和hash类型同样需要处理解码
        elif key_type == 'zset':
            raw_value = conn.zrange(key, 0, -1, withscores=True)
            # 处理zset中的bytes类型，转换为可JSON序列化的格式
            value = []
            for member, score in raw_value:
                # 确保member是字符串而不是bytes
                if isinstance(member, bytes):
                    member = member.decode('utf-8')
                value.append((member, score))
            return {'type': 'zset', 'value': value}, None
        elif key_type == 'hash':
            raw_value = conn.hgetall(key)
            # 处理hash中的bytes类型，转换为可JSON序列化的格式
            value = {}
            for field, val in raw_value.items():
                # 确保field和val都是字符串而不是bytes
                if isinstance(field, bytes):
                    field = field.decode('utf-8')
                if isinstance(val, bytes):
                    val = val.decode('utf-8')
                value[field] = val
            return {'type': 'hash', 'value': value}, None
        else:
            return {'type': 'unknown', 'value': None}, f"不支持的键类型: {key_type}"
    except Exception as e:
        current_app.logger.error(f"获取Redis键值失败: {str(e)}")
        return None, f"获取Redis键值失败: {str(e)}"

def set_redis_key_value(conn, key, value, key_type='string', ttl=None):
    """
    设置Redis键值
    :param conn: Redis连接对象
    :param key: 键名
    :param value: 键值
    :param key_type: 键类型 (string, list, set, zset, hash)
    :param ttl: 过期时间（秒）
    :return: 成功标志和错误信息
    """
    try:
        # 根据类型设置值
        if key_type == 'string':
            conn.set(key, value)
        elif key_type == 'list':
            # 如果是列表类型，先删除旧键，再添加新值
            conn.delete(key)
            if isinstance(value, list):
                conn.rpush(key, *value)
            else:
                conn.rpush(key, value)
        elif key_type == 'set':
            # 如果是集合类型，先删除旧键，再添加新值
            conn.delete(key)
            if isinstance(value, list) or isinstance(value, set):
                conn.sadd(key, *value)
            else:
                conn.sadd(key, value)
        elif key_type == 'zset':
            # 如果是有序集合类型，先删除旧键，再添加新值
            conn.delete(key)
            if isinstance(value, dict):
                for member, score in value.items():
                    conn.zadd(key, {member: score})
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, tuple) and len(item) == 2:
                        conn.zadd(key, {item[0]: item[1]})
        elif key_type == 'hash':
            # 如果是哈希类型，先删除旧键，再添加新值
            conn.delete(key)
            if isinstance(value, dict):
                conn.hset(key, mapping=value)
        else:
            return False, f"不支持的键类型: {key_type}"
        
        # 设置过期时间
        if ttl is not None and int(ttl) > 0:
            conn.expire(key, int(ttl))
            
        return True, None
    except Exception as e:
        current_app.logger.error(f"设置Redis键值失败: {str(e)}")
        return False, f"设置Redis键值失败: {str(e)}"

def delete_redis_key(conn, key):
    """
    删除Redis键
    :param conn: Redis连接对象
    :param key: 键名
    :return: 成功标志和错误信息
    """
    try:
        result = conn.delete(key)
        if result == 1:
            return True, None
        else:
            return False, "键不存在"
    except Exception as e:
        current_app.logger.error(f"删除Redis键失败: {str(e)}")
        return False, f"删除Redis键失败: {str(e)}"