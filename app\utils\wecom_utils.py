import requests
import json
from config import WECOM_WEBHOOK_URL
from flask_jwt_extended import jwt_required, current_user

def send_wecom_message(content='test'):
    username = current_user.username
    content = f"用户{username}在运维平台操作：\n{content}"
    payload = {
        "msgtype": "text",
        "text": {
            "content": content
        }
    }

    # 发送请求
    response = requests.post(WECOM_WEBHOOK_URL, headers={"Content-Type": "application/json"}, data=json.dumps(payload))
    return(response.json())  # 打印返回的 JSON 数据