from flask import Blueprint, request, jsonify, current_app
from app.models.user import User
from werkzeug.security import generate_password_hash
from flask_jwt_extended import jwt_required, current_user
from app import db
from datetime import datetime, timedelta
from app.utils.auth_utils import admin_required, self_or_admin_required
usermanage_bp = Blueprint('permission-management', __name__)

@usermanage_bp.route('/permission-management/users', methods=['GET'])
@jwt_required()
def get_users():
    current_app.logger.info(f"用户搜索GET提交参数: {request.query_string}")
    data = request.args
    username = data.get('username', '')
    phone = data.get('phone', '')
    page_size = int(data.get('pageSize', 10))
    page_num = int(data.get('pageNum', 1))
    
    # 根据用户角色构建查询
    query = User.query
    if not current_user.is_admin:
        # 普通用户只能查看自己的信息
        query = query.filter(User.id == current_user.id)
    else:
        # 管理员可以按条件筛选所有用户
        if username:
            query = query.filter(User.username.like(f'%{username}%'))
        if phone:
            query = query.filter(User.phone.like(f'%{phone}%'))
    
    # 获取总记录数
    total = query.count()
    
    # 分页查询
    users = query.order_by(User.created_at.desc())\
                .offset((page_num - 1) * page_size)\
                .limit(page_size)\
                .all()
    
    # 构建响应数据
    user_list = [{
        'id': str(user.id),
        'username': user.username,
        'email': user.email or '',
        'phone': user.phone or '',
        'role': 'admin' if user.is_admin else 'user',
        'createTime': user.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        'lastLoginTime': user.last_login_time.strftime('%Y-%m-%d %H:%M:%S') if user.last_login_time else ''
    } for user in users]
    
    return jsonify({
        'code': 200,
        'message': '获取用户列表成功',
        'data': {
            'total': total,
            'list': user_list
        }
    })

@usermanage_bp.route('/permission-management/users', methods=['POST'])
@jwt_required()
@admin_required
def create_user():
    try:
        # 获取请求数据
        data = request.get_json()
        current_app.logger.debug(f"创建用户提交参数: {data}")
        # 验证必需字段
        required_fields = ['username', 'password']  # removed 'role' field
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'code': 400,
                    'message': f'缺少必需字段: {field}'
                }), 400
            value = data.get(field)
            if isinstance(value, str):
                value = value.strip()  # 去除首尾空格
            if not value:  # 空字符串、None、空列表/字典等均视为无效
                return jsonify({'code': 400, 'message': f'字段 [{field}] 的值不能为空'}), 400

        # 创建新用户
        new_user = User(
            username=data['username'],
            phone=data.get('phone', ''),
            email=data.get('email', ''),
            created_at=datetime.utcnow()+timedelta(hours=8),
            password_hash=generate_password_hash(data['password']),
            is_admin=data.get('role', False)
        )

        try:
            db.session.add(new_user)
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建用户失败: {str(e)}")
            return jsonify({'code': 3, 'message': '服务器内部错误'}), 500

        # 返回创建的用户信息
        return jsonify({
            'code': 200,
            'message': '创建用户成功',
            'data': {
                'id': str(new_user.id),
                'username': new_user.username,
                'phone': new_user.phone,
                'email': new_user.email,
                'role': new_user.is_admin,
                'created_at': new_user.created_at.strftime('%Y-%m-%d %H:%M:%S')
            }
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'code': 500,
            'message': f'创建用户失败: {str(e)}'
        }), 500

@usermanage_bp.route('/permission-management/users/<int:id>', methods=['DELETE'])
@jwt_required()
@self_or_admin_required('id')
def delete_user(id):

    # 查询用户是否存在
    user = User.query.get(id)
    if not user:
        return jsonify({"code": 404, "message": "用户不存在"}), 404

    try:
        db.session.delete(user)
        db.session.commit()
        return jsonify({"code": 200, "message": "用户删除成功"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"code": 500, "message": "服务器内部错误"}), 500

@usermanage_bp.route('/permission-management/users/<int:id>', methods=['PUT'])
@jwt_required()
@self_or_admin_required('id')
def update_user(id):
    # 查询用户是否存在
    user = User.query.get(id)
    if not user:
        return jsonify({"code": 404, "message": "用户不存在"}), 404

    try:
        # 获取请求数据
        data = request.get_json()
        current_app.logger.debug(f"更新用户提交参数: {data}")
        
        # 更新用户信息
        if 'username' in data and data['username'].strip():
            user.username = data['username'].strip()
        
        if 'phone' in data:
            user.phone = data['phone']
        
        if 'email' in data:
            user.email = data['email']
        
        if 'role' in data:
            # 只有管理员可以修改用户角色
            if not current_user.is_admin:
                return jsonify({"code": 403, "message": "只有管理员可以修改用户角色"}), 403
            # Set is_admin based on role name
            user.is_admin = (data['role'] == 'admin')
        
        if 'password' in data and data['password'].strip():
            user.password_hash = generate_password_hash(data['password'])
        # 提交更改
        db.session.commit()
        
        return jsonify({
            "code": 200,
            "message": "更新成功",
            "data": None
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新用户失败: {str(e)}")
        return jsonify({
            "code": 500,
            "message": f"更新用户失败: {str(e)}"
        }), 500