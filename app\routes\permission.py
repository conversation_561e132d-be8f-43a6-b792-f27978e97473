from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, current_user
from app import db
from app.models.permission import Permission, UserPermission, OperationLog
from app.models.user import User
from app.utils.auth_utils import admin_required
from datetime import datetime, timedelta
from sqlalchemy import func, or_, and_

permission_bp = Blueprint('permission', __name__, url_prefix='/permission')

@permission_bp.route('/permissions', methods=['GET'])
@jwt_required()
@admin_required
def get_permissions():
    """获取所有权限列表"""
    try:
        page_size = int(request.args.get('pageSize', 10))
        page_num = int(request.args.get('pageNum', 1))
        module = request.args.get('module', '')
        function_name = request.args.get('function_name', '')
        
        # 构建查询
        query = Permission.query
        if module:
            query = query.filter(Permission.module.like(f'%{module}%'))
        if function_name:
            query = query.filter(Permission.function_name.like(f'%{function_name}%'))
        
        # 获取总记录数
        total = query.count()
        
        # 分页查询
        permissions = query.order_by(Permission.created_at.desc())\
                          .offset((page_num - 1) * page_size)\
                          .limit(page_size)\
                          .all()
        
        permission_list = [permission.to_dict() for permission in permissions]
        
        return jsonify({
            'code': 200,
            'message': '获取权限列表成功',
            'data': {
                'total': total,
                'list': permission_list
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取权限列表失败: {str(e)}")
        return jsonify({'code': 500, 'message': f'获取权限列表失败: {str(e)}'}), 500

@permission_bp.route('/permissions', methods=['POST'])
@jwt_required()
@admin_required
def create_permission():
    """创建新权限"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'module', 'function_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'code': 400, 'message': f'缺少必填字段: {field}'}), 400
        
        # 检查权限名称是否已存在
        existing_permission = Permission.query.filter_by(name=data['name']).first()
        if existing_permission:
            return jsonify({'code': 400, 'message': '权限名称已存在'}), 400
        
        # 创建新权限
        new_permission = Permission(
            name=data['name'],
            description=data.get('description', ''),
            module=data['module'],
            function_name=data['function_name']
        )
        
        db.session.add(new_permission)
        db.session.commit()
        
        return jsonify({
            'code': 200,
            'message': '创建权限成功',
            'data': new_permission.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建权限失败: {str(e)}")
        return jsonify({'code': 500, 'message': f'创建权限失败: {str(e)}'}), 500

@permission_bp.route('/permissions/<int:permission_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_permission(permission_id):
    """更新权限"""
    try:
        permission = Permission.query.get(permission_id)
        if not permission:
            return jsonify({'code': 404, 'message': '权限不存在'}), 404
        
        data = request.get_json()
        
        # 检查权限名称是否已被其他权限使用
        if data.get('name') and data['name'] != permission.name:
            existing_permission = Permission.query.filter_by(name=data['name']).first()
            if existing_permission:
                return jsonify({'code': 400, 'message': '权限名称已存在'}), 400
        
        # 更新权限信息
        if data.get('name'):
            permission.name = data['name']
        if data.get('description') is not None:
            permission.description = data['description']
        if data.get('module'):
            permission.module = data['module']
        if data.get('function_name'):
            permission.function_name = data['function_name']
        
        db.session.commit()
        
        return jsonify({
            'code': 200,
            'message': '更新权限成功',
            'data': permission.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新权限失败: {str(e)}")
        return jsonify({'code': 500, 'message': f'更新权限失败: {str(e)}'}), 500

@permission_bp.route('/permissions/<int:permission_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_permission(permission_id):
    """删除权限"""
    try:
        permission = Permission.query.get(permission_id)
        if not permission:
            return jsonify({'code': 404, 'message': '权限不存在'}), 404
        
        # 检查是否有用户正在使用该权限
        user_permissions = UserPermission.query.filter_by(permission_id=permission_id, is_active=True).count()
        if user_permissions > 0:
            return jsonify({'code': 400, 'message': '该权限正在被用户使用，无法删除'}), 400
        
        db.session.delete(permission)
        db.session.commit()
        
        return jsonify({'code': 200, 'message': '删除权限成功'})
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除权限失败: {str(e)}")
        return jsonify({'code': 500, 'message': f'删除权限失败: {str(e)}'}), 500

@permission_bp.route('/user-permissions', methods=['GET'])
@jwt_required()
def get_user_permissions():
    """获取用户权限列表"""
    try:
        page_size = int(request.args.get('pageSize', 10))
        page_num = int(request.args.get('pageNum', 1))
        user_id = request.args.get('user_id')
        username = request.args.get('username', '')
        permission_name = request.args.get('permission_name', '')

        # 构建查询
        query = db.session.query(UserPermission).join(Permission).join(User, UserPermission.user_id == User.id)

        if current_user.is_admin:
            # 如果指定了user_id，则过滤特定用户
            if user_id:
                query = query.filter(UserPermission.user_id == user_id)
            # 如果指定了username，则根据用户名过滤
            if username:
                query = query.filter(User.username.like(f'%{username}%'))
        else:
            # 普通用户只能查看自己的权限，忽略user_id和username参数
            query = UserPermission.query.filter(UserPermission.user_id == current_user.id)

        # if user_id:
        #     query = query.filter(UserPermission.user_id == user_id)
        if permission_name:
            query = query.filter(Permission.name.like(f'%{permission_name}%'))
        
        # 获取总记录数
        total = query.count()
        
        # 分页查询
        user_permissions = query.order_by(UserPermission.granted_at.desc())\
                               .offset((page_num - 1) * page_size)\
                               .limit(page_size)\
                               .all()
        
        permission_list = []
        for up in user_permissions:
            data = up.to_dict()
            data['username'] = up.user.username
            permission_list.append(data)
        
        return jsonify({
            'code': 200,
            'message': '获取用户权限列表成功',
            'data': {
                'total': total,
                'list': permission_list
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取用户权限列表失败: {str(e)}")
        return jsonify({'code': 500, 'message': f'获取用户权限列表失败: {str(e)}'}), 500

@permission_bp.route('/user-permissions', methods=['POST'])
@jwt_required()
@admin_required
def grant_user_permission():
    """为用户授权"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['user_id', 'permission_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'code': 400, 'message': f'缺少必填字段: {field}'}), 400
        
        # 检查用户是否存在
        user = User.query.get(data['user_id'])
        if not user:
            return jsonify({'code': 404, 'message': '用户不存在'}), 404
        
        # 检查权限是否存在
        permission = Permission.query.get(data['permission_id'])
        if not permission:
            return jsonify({'code': 404, 'message': '权限不存在'}), 404
        
        # 检查用户是否已有该权限
        existing_permission = UserPermission.query.filter_by(
            user_id=data['user_id'],
            permission_id=data['permission_id']
        ).first()
        
        if existing_permission:
            if existing_permission.is_active:
                return jsonify({'code': 400, 'message': '用户已拥有该权限'}), 400
            else:
                # 重新激活权限
                existing_permission.is_active = True
                existing_permission.granted_by = current_user.id
                existing_permission.granted_at = datetime.utcnow()
        else:
            # 创建新的用户权限
            new_user_permission = UserPermission(
                user_id=data['user_id'],
                permission_id=data['permission_id'],
                granted_by=current_user.id
            )
            db.session.add(new_user_permission)
        
        db.session.commit()
        
        return jsonify({'code': 200, 'message': '授权成功'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"授权失败: {str(e)}")
        return jsonify({'code': 500, 'message': f'授权失败: {str(e)}'}), 500

@permission_bp.route('/user-permissions/<int:user_permission_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def revoke_user_permission(user_permission_id):
    """撤销用户权限"""
    try:
        user_permission = UserPermission.query.get(user_permission_id)
        if not user_permission:
            return jsonify({'code': 404, 'message': '用户权限不存在'}), 404

        # 设置权限为非激活状态
        user_permission.is_active = False
        db.session.commit()

        return jsonify({'code': 200, 'message': '撤销权限成功'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"撤销权限失败: {str(e)}")
        return jsonify({'code': 500, 'message': f'撤销权限失败: {str(e)}'}), 500

@permission_bp.route('/check-permission', methods=['POST'])
@jwt_required()
def check_permission():
    """检查用户是否有指定权限"""
    try:
        data = request.get_json()
        permission_name = data.get('permission_name')

        if not permission_name:
            return jsonify({'code': 400, 'message': '缺少权限名称'}), 400

        # 管理员拥有所有权限
        if current_user.is_admin:
            has_permission = True
        else:
            # 检查用户权限
            permission_query = db.session.query(UserPermission).join(Permission).filter(
                UserPermission.user_id == current_user.id,
                Permission.name == permission_name,
                UserPermission.is_active == True
            ).first()
            has_permission = permission_query is not None

        return jsonify({
            'code': 200,
            'message': 'success',
            'data': {
                'has_permission': has_permission,
                'permission_name': permission_name,
                'user_id': current_user.id,
                'is_admin': current_user.is_admin
            }
        })

    except Exception as e:
        current_app.logger.error(f"检查权限失败: {str(e)}")
        return jsonify({'code': 500, 'message': f'检查权限失败: {str(e)}'}), 500

@permission_bp.route('/operation-logs', methods=['GET'])
@jwt_required()
@admin_required
def get_operation_logs():
    """获取操作日志 - 管理员可以查看所有用户的日志，普通用户只能查看自己的日志"""
    try:
        page_size = int(request.args.get('pageSize', 10))
        page_num = int(request.args.get('pageNum', 1))
        user_id = request.args.get('user_id')
        module = request.args.get('module', '')
        function_name = request.args.get('function_name', '')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # 构建查询
        if current_user.is_admin:
            # 管理员可以查看所有用户的日志
            query = OperationLog.query.join(User)
            # 如果指定了user_id，则过滤特定用户
            if user_id:
                query = query.filter(OperationLog.user_id == user_id)
        else:
            # 普通用户只能查看自己的日志，忽略user_id参数
            query = OperationLog.query.filter(OperationLog.user_id == current_user.id)

        # 通用过滤条件
        if module:
            query = query.filter(OperationLog.module.like(f'%{module}%'))
        if function_name:
            query = query.filter(OperationLog.function_name.like(f'%{function_name}%'))
        if start_date:
            try:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                query = query.filter(OperationLog.created_at >= start_dt)
            except ValueError:
                return jsonify({'code': 400, 'message': '开始日期格式错误，应为YYYY-MM-DD'}), 400
        if end_date:
            try:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                query = query.filter(OperationLog.created_at < end_dt)
            except ValueError:
                return jsonify({'code': 400, 'message': '结束日期格式错误，应为YYYY-MM-DD'}), 400

        # 获取总记录数
        total = query.count()

        # 分页查询
        logs = query.order_by(OperationLog.created_at.desc())\
                   .offset((page_num - 1) * page_size)\
                   .limit(page_size)\
                   .all()

        log_list = [log.to_dict() for log in logs]

        return jsonify({
            'code': 200,
            'message': '获取操作日志成功',
            'data': {
                'total': total,
                'list': log_list,
                'is_admin': current_user.is_admin  # 返回用户角色信息，便于前端判断
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取操作日志失败: {str(e)}")
        return jsonify({'code': 500, 'message': f'获取操作日志失败: {str(e)}'}), 500

@permission_bp.route('/my-permissions', methods=['GET'])
@jwt_required()
def get_my_permissions():
    """获取当前用户的权限列表,目前已关闭前端页面20250708"""
    try:
        # 管理员拥有所有权限
        if current_user.is_admin:
            permissions = Permission.query.all()
            permission_list = [permission.to_dict() for permission in permissions]
        else:
            # 普通用户只能看到自己拥有的权限
            user_permissions = db.session.query(UserPermission).join(Permission).filter(
                UserPermission.user_id == current_user.id,
                UserPermission.is_active == True
            ).all()
            permission_list = [up.permission.to_dict() for up in user_permissions]

        return jsonify({
            'code': 200,
            'message': '获取我的权限成功',
            'data': {
                'total': len(permission_list),
                'list': permission_list,
                'is_admin': current_user.is_admin
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取我的权限失败: {str(e)}")
        return jsonify({'code': 500, 'message': f'获取我的权限失败: {str(e)}'}), 500
