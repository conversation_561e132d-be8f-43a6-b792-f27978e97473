from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.user import User
from app.utils.apollo_utils import (
    check_apollo_config_status,
    check_apollo_multiple_envs,
    check_apollo_urls_batch
)
import logging

# 创建蓝图
apollo_bp = Blueprint('apollo', __name__)

# 配置日志
logger = logging.getLogger(__name__)

@apollo_bp.route('/apollo/check-single', methods=['POST'])
@jwt_required()
def check_single_config():
    """
    检查单个Apollo配置状态
    """
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401
        
        # 获取请求数据
        data = request.json or {}
        url = data.get('url', '').strip()
        token = data.get('token', '').strip()

        if not url:
            return jsonify({
                'success': False,
                'message': 'URL参数不能为空',
                'code': 400
            }), 400

        # 验证URL格式
        if not url.startswith('https://apollo.serviceshare.com/config.html'):
            return jsonify({
                'success': False,
                'message': '无效的Apollo配置URL',
                'code': 400
            }), 400

        # 检查配置状态 (默认启用调试模式)
        debug = data.get('debug', True)
        result = check_apollo_config_status(url, debug=debug, token=token)
        
        return jsonify({
            'success': True,
            'message': '配置状态检查完成',
            'data': result,
            'code': 200
        })
        
    except Exception as e:
        logger.error(f"检查单个Apollo配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'检查配置状态失败: {str(e)}',
            'code': 500
        }), 500

@apollo_bp.route('/apollo/check-multi-env', methods=['POST'])
@jwt_required()
def check_multi_environment():
    """
    检查指定应用在多个环境中的配置状态
    
    请求体:
    {
        "appid": "carpub-system",
        "environments": ["SBY", "LTY", "PRO"],
        "cluster": "default",
        "token": "your_apollo_api_token"  // 可选，如果不提供将使用配置文件中的默认token
    }
    """
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401
        
        # 获取请求数据
        data = request.json or {}
        appid = data.get('appid', '').strip()
        environments = data.get('environments', ['SBY', 'LTY', 'PRO'])
        cluster = data.get('cluster', 'default')
        token = data.get('token', '').strip()

        if not appid:
            return jsonify({
                'success': False,
                'message': 'appid参数不能为空',
                'code': 400
            }), 400

        if not environments or not isinstance(environments, list):
            return jsonify({
                'success': False,
                'message': 'environments参数必须是非空数组',
                'code': 400
            }), 400

        # 检查多环境配置状态 (默认启用调试模式)
        debug = data.get('debug', True)
        result = check_apollo_multiple_envs(appid, environments, cluster, debug=debug, token=token)
        
        return jsonify({
            'success': True,
            'message': '多环境配置状态检查完成',
            'data': result,
            'code': 200
        })
        
    except Exception as e:
        logger.error(f"检查多环境Apollo配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'检查多环境配置状态失败: {str(e)}',
            'code': 500
        }), 500

@apollo_bp.route('/apollo/check-batch', methods=['POST'])
@jwt_required()
def check_batch_configs():
    """
    批量检查多个Apollo配置URL
    
    请求体:
    {
        "urls": [
            "https://apollo.serviceshare.com/config.html#/appid=carpub-system&env=SBY&cluster=default",
            "https://apollo.serviceshare.com/config.html#/appid=carpub-system&env=LTY&cluster=default"
        ]
    }
    """
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401
        
        # 获取请求数据
        data = request.json or {}
        urls = data.get('urls', [])
        
        if not urls or not isinstance(urls, list):
            return jsonify({
                'success': False,
                'message': 'urls参数必须是非空数组',
                'code': 400
            }), 400
        
        # 验证URL格式
        invalid_urls = []
        for url in urls:
            if not isinstance(url, str) or not url.startswith('https://apollo.serviceshare.com/config.html'):
                invalid_urls.append(url)
        
        if invalid_urls:
            return jsonify({
                'success': False,
                'message': f'包含无效的Apollo配置URL: {invalid_urls}',
                'code': 400
            }), 400
        
        # 批量检查配置状态 (默认启用调试模式)
        debug = data.get('debug', True)
        result = check_apollo_urls_batch(urls, debug=debug)
        
        return jsonify({
            'success': True,
            'message': '批量配置状态检查完成',
            'data': result,
            'code': 200
        })
        
    except Exception as e:
        logger.error(f"批量检查Apollo配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'批量检查配置状态失败: {str(e)}',
            'code': 500
        }), 500

@apollo_bp.route('/apollo/quick-check', methods=['POST'])
@jwt_required()
def quick_check():
    """
    快速检查常用应用的配置状态
    
    请求体:
    {
        "appids": ["carpub-system", "cloud-caring"],
        "environments": ["SBY", "LTY", "PRO"]
    }
    """
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401
        
        # 获取请求数据
        data = request.json or {}
        appids = data.get('appids', ['carpub-system'])
        environments = data.get('environments', ['SBY', 'LTY', 'PRO'])
        cluster = data.get('cluster', 'default')
        
        if not appids or not isinstance(appids, list):
            return jsonify({
                'success': False,
                'message': 'appids参数必须是非空数组',
                'code': 400
            }), 400
        
        # 构建URL列表
        urls = []
        for appid in appids:
            for env in environments:
                url = f"https://apollo.serviceshare.com/config.html#/appid={appid}&env={env}&cluster={cluster}"
                urls.append(url)
        
        # 批量检查配置状态
        debug = data.get('debug', True)
        result = check_apollo_urls_batch(urls, debug=debug)
        
        # 重新组织结果按应用分组
        organized_result = {
            'summary': result.get('summary', {}),
            'applications': {}
        }
        
        for res in result.get('results', []):
            appid = res.get('appid', 'unknown')
            if appid not in organized_result['applications']:
                organized_result['applications'][appid] = {
                    'appid': appid,
                    'environments': {},
                    'summary': {
                        'total_envs': 0,
                        'pending_envs': 0,
                        'success_count': 0,
                        'error_count': 0
                    }
                }
            
            env = res.get('env', 'unknown')
            organized_result['applications'][appid]['environments'][env] = res
            organized_result['applications'][appid]['summary']['total_envs'] += 1
            
            if res.get('success', False):
                organized_result['applications'][appid]['summary']['success_count'] += 1
                if res.get('has_pending_release', False):
                    organized_result['applications'][appid]['summary']['pending_envs'] += 1
            else:
                organized_result['applications'][appid]['summary']['error_count'] += 1
        
        return jsonify({
            'success': True,
            'message': '快速检查完成',
            'data': organized_result,
            'code': 200
        })
        
    except Exception as e:
        logger.error(f"快速检查Apollo配置失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'快速检查失败: {str(e)}',
            'code': 500
        }), 500
