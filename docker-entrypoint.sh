#!/bin/bash

# Docker容器启动脚本
set -e

# 设置Oracle环境变量
export LD_LIBRARY_PATH=/opt/oracle/instantclient_11_2:$LD_LIBRARY_PATH
export ORACLE_HOME=/opt/oracle/instantclient_11_2

# 检查Oracle客户端是否可用
echo "检查Oracle客户端..."
if [ -d "/opt/oracle/instantclient_11_2" ]; then
    echo "Oracle Instant Client 目录存在"
    ls -la /opt/oracle/instantclient_11_2/
else
    echo "警告: Oracle Instant Client 目录不存在，Oracle相关功能可能无法使用"
fi

# 更新动态链接库缓存
ldconfig

# 切换到应用目录
cd /app

# 等待数据库连接就绪（可选）
echo "等待数据库连接就绪..."
python -c "
import time
import sys
import pymysql
import config

max_retries = 30
retry_count = 0

while retry_count < max_retries:
    try:
        connection = pymysql.connect(
            host=config.MYSQL_HOST,
            user=config.MYSQL_USER,
            password=config.MYSQL_PASSWORD,
            database=config.MYSQL_DB,
            charset='utf8mb4'
        )
        connection.close()
        print('数据库连接成功')
        break
    except Exception as e:
        retry_count += 1
        print(f'数据库连接失败 (尝试 {retry_count}/{max_retries}): {e}')
        if retry_count >= max_retries:
            print('数据库连接超时，继续启动应用...')
            break
        time.sleep(2)
"

# 创建数据库表（如果需要）
echo "初始化数据库表..."
python -c "
from app import create_app, db
app = create_app()
with app.app_context():
    try:
        db.create_all()
        print('数据库表初始化完成')
    except Exception as e:
        print(f'数据库表初始化失败: {e}')
"

# 启动应用
echo "启动Flask应用..."
exec python app.py
