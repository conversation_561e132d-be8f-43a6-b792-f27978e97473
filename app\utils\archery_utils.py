import requests
import re
import json
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from datetime import datetime
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON>Executor

from config import SQL_PLATFORM_CONFIG



def get_sql_workflow_status_text(workflow_id, debug=False):
    """
    Extract status text from SQL platform detail page

    Args:
        workflow_id (int/str): The workflow ID to query
        debug (bool): Enable debug logging

    Returns:
        dict: Contains success status, extracted text, and error information
    """
    def _debug_print(message):
        """Print debug message if debug mode is enabled"""
        if debug:
            print(f"🔧 [DEBUG] {message}")

    # Create session with proper headers
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    })

    # Set timeout to prevent hanging
    timeout = 30

    try:
        # Step 1: Access login page to get initial session cookies and CSRF token
        base_url = SQL_PLATFORM_CONFIG['base_url']
        login_page_url = SQL_PLATFORM_CONFIG['login_url']
        _debug_print(f"Accessing login page: {login_page_url}")

        login_page_response = session.get(login_page_url, timeout=timeout)
        _debug_print(f"Login page response status: {login_page_response.status_code}")

        if login_page_response.status_code != 200:
            return {
                "success": False,
                "error": f"Failed to access login page. Status code: {login_page_response.status_code}",
                "step": "login_page_access",
                "extracted_text": None
            }

        # Step 2: Extract CSRF token
        _debug_print("Extracting CSRF token from login page...")
        csrf_match = re.search(r'name="csrfmiddlewaretoken" value="([^"]+)"', login_page_response.text)
        if not csrf_match:
            return {
                "success": False,
                "error": "CSRF token not found in login page",
                "step": "csrf_extraction",
                "extracted_text": None
            }

        csrf_token = csrf_match.group(1)
        _debug_print(f"Found CSRF token: {csrf_token[:20]}...")

        # Step 3: Perform authentication
        auth_url = urljoin(base_url, "/authenticate/")
        _debug_print(f"Performing authentication at: {auth_url}")

        # Prepare login data with CSRF token
        login_data = {
            "username": SQL_PLATFORM_CONFIG['username'],
            "password": SQL_PLATFORM_CONFIG['password'],
            "csrfmiddlewaretoken": csrf_token
        }

        # Set authentication headers with CSRF token
        auth_headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": login_page_url,
            "X-CSRFToken": csrf_token
        }

        # Perform authentication request
        auth_response = session.post(
            auth_url,
            data=login_data,
            headers=auth_headers,
            allow_redirects=True,
            timeout=timeout
        )

        _debug_print(f"Authentication response status: {auth_response.status_code}")
        _debug_print(f"Final URL after redirects: {auth_response.url}")

        # Check if authentication was successful
        if auth_response.status_code == 200:
            # Check if response is JSON (as expected by the platform)
            try:
                auth_json = auth_response.json()
                _debug_print(f"Auth JSON response: {auth_json}")

                if auth_json.get('status') != 0:
                    return {
                        "success": False,
                        "error": f"Authentication failed: {auth_json.get('msg', 'Unknown error')}",
                        "step": "authentication",
                        "extracted_text": None
                    }

                _debug_print("Authentication successful")

            except json.JSONDecodeError:
                # Fallback to URL-based check if not JSON
                if "/login" in auth_response.url or "/authenticate" in auth_response.url:
                    return {
                        "success": False,
                        "error": "Authentication failed - redirected back to login page",
                        "step": "authentication",
                        "final_url": auth_response.url,
                        "extracted_text": None
                    }

                _debug_print("Authentication successful (non-JSON response)")

        else:
            return {
                "success": False,
                "error": f"Authentication failed. Status code: {auth_response.status_code}",
                "step": "authentication",
                "extracted_text": None
            }

        # Step 4: Access the detail page
        detail_url = urljoin(base_url, f"/detail/{workflow_id}/")
        _debug_print(f"Accessing detail page: {detail_url}")

        detail_response = session.get(detail_url, timeout=timeout)
        _debug_print(f"Detail page response status: {detail_response.status_code}")

        if detail_response.status_code != 200:
            return {
                "success": False,
                "error": f"Failed to retrieve detail page. Status code: {detail_response.status_code}",
                "url": detail_url,
                "status_code": detail_response.status_code,
                "extracted_text": None
            }

        _debug_print(f"Successfully retrieved detail page content ({len(detail_response.text)} characters)")

        # Step 5: Parse HTML and extract text from specified XPATH
        try:
            soup = BeautifulSoup(detail_response.text, 'html.parser')

            # Save HTML for debugging if debug mode is enabled
            if debug:
                debug_filename = f"debug_html_{workflow_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
                try:
                    with open(debug_filename, 'w', encoding='utf-8') as f:
                        f.write(detail_response.text)
                    _debug_print(f"HTML content saved to: {debug_filename}")
                except Exception as save_error:
                    _debug_print(f"Failed to save HTML file: {save_error}")

            # Find the element using the XPATH: //*[@id="detail"]/div[3]/div[2]/div[2]/table/tbody/tr/td
            # Try multiple approaches to find the target element

            # Approach 1: Direct XPATH conversion
            detail_div = soup.find('div', id='detail')
            if not detail_div:
                return {
                    "success": False,
                    "error": "Could not find element with id='detail'",
                    "step": "xpath_parsing",
                    "extracted_text": None
                }

            _debug_print(f"Found detail div with {len(detail_div.find_all('div', recursive=False))} direct div children")

            # Try to find table elements within the detail div
            tables = detail_div.find_all('table')
            _debug_print(f"Found {len(tables)} table elements in detail div")

            # Look for the specific table structure
            extracted_text = None

            # Method 1: Try the exact XPATH structure
            divs = detail_div.find_all('div', recursive=False)
            if len(divs) >= 3:
                third_div = divs[2]  # div[3] (0-indexed)
                sub_divs = third_div.find_all('div', recursive=False)
                if len(sub_divs) >= 2:
                    second_sub_div = sub_divs[1]  # div[2] (0-indexed)
                    final_divs = second_sub_div.find_all('div', recursive=False)
                    if len(final_divs) >= 2:
                        final_div = final_divs[1]  # div[2] (0-indexed)
                        table = final_div.find('table')
                        if table:
                            tbody = table.find('tbody')
                            if tbody:
                                tr = tbody.find('tr')
                                if tr:
                                    td = tr.find('td')
                                    if td:
                                        extracted_text = td.get_text(strip=True)
                                        _debug_print("Successfully extracted text using exact XPATH method")

            # Method 2: Search for tables with specific patterns if Method 1 failed
            if not extracted_text:
                _debug_print("Exact XPATH method failed, trying pattern-based search...")
                for table in tables:
                    tbody = table.find('tbody')
                    if tbody:
                        rows = tbody.find_all('tr')
                        for row in rows:
                            cells = row.find_all('td')
                            for cell in cells:
                                cell_text = cell.get_text(strip=True)
                                # Look for status-related keywords including cancelled status
                                keywords = ['等待审批', '执行结果', '下级审批', '已正常结束', '执行有异常', '取消原因:', '取消原因：']
                                if any(keyword in cell_text for keyword in keywords):
                                    extracted_text = cell_text
                                    _debug_print(f"Successfully extracted text using pattern-based search: {cell_text[:100]}...")
                                    break
                            if extracted_text:
                                break
                    if extracted_text:
                        break

            # Method 3: Fallback - search all td elements for status text
            if not extracted_text:
                _debug_print("Pattern-based search failed, trying fallback method...")
                all_tds = detail_div.find_all('td')
                _debug_print(f"Found {len(all_tds)} td elements in detail div")
                for td in all_tds:
                    td_text = td.get_text(strip=True)
                    keywords = ['等待审批', '执行结果', '下级审批', '已正常结束', '执行有异常', '取消原因:', '取消原因：']
                    if any(keyword in td_text for keyword in keywords):
                        extracted_text = td_text
                        _debug_print(f"Successfully extracted text using fallback method: {td_text[:100]}...")
                        break

            if not extracted_text:
                return {
                    "success": False,
                    "error": f"Could not find status text in any table cells. Found {len(tables)} tables and {len(detail_div.find_all('td'))} td elements.",
                    "step": "xpath_parsing",
                    "extracted_text": None
                }

            # If we found text, return success
            _debug_print(f"Successfully extracted text: {extracted_text[:100]}...")

            return {
                "success": True,
                "extracted_text": extracted_text,
                "url": detail_url,
                "workflow_id": workflow_id,
                "step": "completed"
            }

        except Exception as parse_error:
            return {
                "success": False,
                "error": f"Failed to parse HTML and extract text: {str(parse_error)}",
                "step": "xpath_parsing",
                "extracted_text": None
            }

    except requests.exceptions.Timeout:
        return {
            "success": False,
            "error": "Request timed out",
            "step": "network_timeout",
            "extracted_text": None
        }
    except requests.exceptions.ConnectionError as e:
        return {
            "success": False,
            "error": f"Connection error: {str(e)}",
            "step": "network_connection",
            "extracted_text": None
        }
    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "error": f"Network error: {str(e)}",
            "step": "network_error",
            "extracted_text": None
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"Unexpected error: {str(e)}",
            "step": "unexpected_error",
            "extracted_text": None
        }


def analyze_status_text(text):
    """
    分析状态文本并返回对应的状态信息

    Args:
        text (str): 状态文本

    Returns:
        dict: 状态信息
    """
    text = text.strip()

    # 状态匹配规则 - 根据用户需求更新的精确匹配
    # 检查取消状态 - 优先检查，因为取消状态可能包含其他关键词
    # 支持中文冒号和英文冒号
    if "取消原因:" in text or "取消原因：" in text:
        # 提取取消原因的具体内容
        cancel_reason = ""
        if "取消原因:" in text:
            cancel_reason = text.split("取消原因:", 1)[1].strip() if "取消原因:" in text else ""
        elif "取消原因：" in text:
            cancel_reason = text.split("取消原因：", 1)[1].strip() if "取消原因：" in text else ""

        # 根据取消原因提供更具体的描述
        if "已执行" in cancel_reason:
            description = "工单已被取消，原因：已执行"
            status = "工单已取消(已执行)"
        elif "线下执行" in cancel_reason:
            description = "工单已被取消，原因：线下执行"
            status = "工单已取消(线下执行)"
        else:
            description = f"工单已被取消，原因：{cancel_reason}" if cancel_reason else "工单已被取消"
            status = "工单已取消"

        return {
            "status": status,
            "description": description,
            "raw_text": text,
            "status_code": "WORKFLOW_CANCELLED",
            "cancel_reason": cancel_reason
        }

    elif "等待审批" in text and "管理组->运维组" in text:
        return {
            "status": "等待部门主管审批",
            "description": "工单正在等待部门主管审批",
            "raw_text": text,
            "status_code": "PENDING_MANAGER_APPROVAL"
        }

    elif "执行结果：执行有异常" in text:
        return {
            "status": "SQL执行错误",
            "description": "SQL执行过程中出现异常",
            "raw_text": text,
            "status_code": "SQL_EXECUTION_ERROR"
        }

    elif "下级审批：运维组" in text:
        return {
            "status": "等待运维组审批",
            "description": "工单正在等待运维组审批",
            "raw_text": text,
            "status_code": "PENDING_OPS_APPROVAL"
        }

    elif "下级审批：None" in text:
        return {
            "status": "等待执行",
            "description": "工单审批完成，等待执行",
            "raw_text": text,
            "status_code": "PENDING_EXECUTION"
        }

    elif "执行结果：已正常结束" in text:
        return {
            "status": "执行完成",
            "description": "工单已成功执行完成",
            "raw_text": text,
            "status_code": "COMPLETED_SUCCESSFULLY"
        }

    # 未匹配到已知状态
    return {
        "status": "未知",
        "description": "未识别的状态",
        "raw_text": text,
        "status_code": "UNKNOWN"
    }


def get_status_icon(status_code):
    """
    根据状态代码返回对应的图标

    Args:
        status_code (str): 状态代码

    Returns:
        str: 对应的图标
    """
    icons = {
        "PENDING_MANAGER_APPROVAL": "⏳",  # 等待部门主管审批
        "SQL_EXECUTION_ERROR": "❌",       # SQL执行错误
        "PENDING_OPS_APPROVAL": "🔄",     # 等待运维组审批
        "PENDING_EXECUTION": "⏰",        # 等待执行
        "COMPLETED_SUCCESSFULLY": "✅",   # 执行完成
        "WORKFLOW_CANCELLED": "🚫",       # 工单已取消
        "UNKNOWN": "❓"                   # 未知状态
    }
    return icons.get(status_code, "❓")


def get_status_suggestion(status_code):
    """
    根据状态代码返回操作建议

    Args:
        status_code (str): 状态代码

    Returns:
        str: 操作建议
    """
    suggestions = {
        "PENDING_MANAGER_APPROVAL": "请联系部门主管进行审批",
        "SQL_EXECUTION_ERROR": "请检查SQL语句语法，修复后重新提交",
        "PENDING_OPS_APPROVAL": "请联系运维组进行审批",
        "PENDING_EXECUTION": "工单已审批完成，请等待系统执行",
        "COMPLETED_SUCCESSFULLY": "工单已成功完成，可以验证执行结果",
        "WORKFLOW_CANCELLED": "工单已被取消，请查看取消原因。如需重新提交请创建新工单",
        "UNKNOWN": "请手动检查工单状态或联系管理员"
    }
    return suggestions.get(status_code, "")


def get_sql_workflow_status_with_analysis(workflow_id, debug=False):
    """
    获取SQL工单状态文本并进行分析

    Args:
        workflow_id (int/str): 工单ID
        debug (bool): 是否启用调试模式

    Returns:
        dict: 包含原始文本、分析结果和建议的完整信息，适用于前端API响应
    """
    # 获取原始状态文本
    result = get_sql_workflow_status_text(workflow_id, debug)

    if not result["success"]:
        # 返回失败结果，只包含必要字段
        return {
            "success": False,
            "error": result.get("error"),
            "workflow_id": workflow_id,
            "url": result.get("url"),
            "status": None,
            "description": None,
            "status_code": None,
            "suggestion": None,
            "extracted_text": None
        }

    # 分析状态文本
    extracted_text = result["extracted_text"]
    analysis = analyze_status_text(extracted_text)

    # 获取建议
    suggestion = get_status_suggestion(analysis["status_code"])

    # 返回前端友好的结构化数据
    return {
        "success": True,
        "workflow_id": workflow_id,
        "url": result.get("url"),
        "status": analysis["status"],
        "description": analysis["description"],
        "status_code": analysis["status_code"],
        "suggestion": suggestion,
        "extracted_text": extracted_text
    }


def test_sql_workflow_status(workflow_id=6318, debug=True):
    """
    测试SQL工单状态查询功能

    Args:
        workflow_id (int): 要测试的工单ID，默认为6318
        debug (bool): 是否启用调试模式，默认为True

    Returns:
        dict: 测试结果
    """
    print(f"🧪 测试SQL工单状态查询功能 (工单ID: {workflow_id})")
    print("=" * 60)

    # 测试基本功能
    result = get_sql_workflow_status_with_analysis(workflow_id, debug)

    if result["success"]:
        print(f"\n✅ 测试成功!")
        print(f"📄 提取的状态文本: {result['extracted_text']}")
        print(f"🔍 分析结果: {result['status']} ({result['status_code']})")
        if result.get('suggestion'):
            print(f"💡 操作建议: {result['suggestion']}")
    else:
        print(f"\n❌ 测试失败: {result['error']}")

    return result


def batch_query_sql_workflows(workflow_ids, debug=False, max_workers=3):
    """
    批量查询多个SQL工单状态

    Args:
        workflow_ids (list): 工单ID列表
        debug (bool): 是否启用调试模式
        max_workers (int): 最大并发线程数，默认为3

    Returns:
        dict: 包含所有查询结果的字典
    """
    results = {
        'success_count': 0,
        'failed_count': 0,
        'total_count': len(workflow_ids),
        'details': []
    }

    def query_single_workflow(workflow_id):
        """查询单个工单状态"""
        return get_sql_workflow_status_with_analysis(workflow_id, debug=debug)

    # 使用线程池并发查询
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        query_results = list(executor.map(query_single_workflow, workflow_ids))

    # 处理结果
    for result in query_results:
        if result["success"]:
            results['success_count'] += 1
        else:
            results['failed_count'] += 1
        results['details'].append(result)

    return results


def approve_sql_workflow(workflow_id, debug=False):
    """
    审批通过SQL工单

    Args:
        workflow_id (int/str): 工单ID
        debug (bool): 是否启用调试模式

    Returns:
        dict: 审批结果
    """
    def _debug_print(message):
        """Print debug message if debug mode is enabled"""
        if debug:
            print(f"🔧 [DEBUG] {message}")

    # Create session with proper headers
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    })

    # Set timeout to prevent hanging
    timeout = 30

    try:
        # Step 1: Access login page to get initial session cookies and CSRF token
        base_url = SQL_PLATFORM_CONFIG['base_url']
        login_page_url = SQL_PLATFORM_CONFIG['login_url']
        _debug_print(f"Accessing login page: {login_page_url}")

        login_page_response = session.get(login_page_url, timeout=timeout)
        _debug_print(f"Login page response status: {login_page_response.status_code}")

        if login_page_response.status_code != 200:
            return {
                "success": False,
                "error": f"Failed to access login page. Status code: {login_page_response.status_code}",
                "step": "login_page_access",
                "workflow_id": workflow_id
            }

        # Step 2: Extract CSRF token
        _debug_print("Extracting CSRF token from login page...")
        csrf_match = re.search(r'name="csrfmiddlewaretoken" value="([^"]+)"', login_page_response.text)
        if not csrf_match:
            return {
                "success": False,
                "error": "CSRF token not found in login page",
                "step": "csrf_extraction",
                "workflow_id": workflow_id
            }

        csrf_token = csrf_match.group(1)
        _debug_print(f"Found CSRF token: {csrf_token[:20]}...")

        # Step 3: Perform authentication
        auth_url = urljoin(base_url, "/authenticate/")
        _debug_print(f"Performing authentication at: {auth_url}")

        # Prepare login data with CSRF token
        login_data = {
            "username": SQL_PLATFORM_CONFIG['username'],
            "password": SQL_PLATFORM_CONFIG['password'],
            "csrfmiddlewaretoken": csrf_token
        }

        # Set authentication headers with CSRF token
        auth_headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": login_page_url,
            "X-CSRFToken": csrf_token
        }

        # Perform authentication request
        auth_response = session.post(
            auth_url,
            data=login_data,
            headers=auth_headers,
            allow_redirects=True,
            timeout=timeout
        )

        _debug_print(f"Authentication response status: {auth_response.status_code}")

        # Check if authentication was successful
        if auth_response.status_code == 200:
            try:
                auth_json = auth_response.json()
                _debug_print(f"Auth JSON response: {auth_json}")

                if auth_json.get('status') != 0:
                    return {
                        "success": False,
                        "error": f"Authentication failed: {auth_json.get('msg', 'Unknown error')}",
                        "step": "authentication",
                        "workflow_id": workflow_id
                    }

                _debug_print("Authentication successful")

            except json.JSONDecodeError:
                if "/login" in auth_response.url or "/authenticate" in auth_response.url:
                    return {
                        "success": False,
                        "error": "Authentication failed - redirected back to login page",
                        "step": "authentication",
                        "workflow_id": workflow_id
                    }

                _debug_print("Authentication successful (non-JSON response)")

        else:
            return {
                "success": False,
                "error": f"Authentication failed. Status code: {auth_response.status_code}",
                "step": "authentication",
                "workflow_id": workflow_id
            }

        # Step 4: Get workflow detail page to extract CSRF token for approval
        detail_url = urljoin(base_url, f"/detail/{workflow_id}/")
        _debug_print(f"Accessing detail page: {detail_url}")

        detail_response = session.get(detail_url, timeout=timeout)
        _debug_print(f"Detail page response status: {detail_response.status_code}")

        if detail_response.status_code != 200:
            return {
                "success": False,
                "error": f"Failed to retrieve detail page. Status code: {detail_response.status_code}",
                "step": "detail_page_access",
                "workflow_id": workflow_id
            }

        # Step 5: Extract CSRF token from detail page
        csrf_match = re.search(r'name="csrfmiddlewaretoken" value="([^"]+)"', detail_response.text)
        if not csrf_match:
            return {
                "success": False,
                "error": "CSRF token not found in detail page",
                "step": "detail_csrf_extraction",
                "workflow_id": workflow_id
            }

        detail_csrf_token = csrf_match.group(1)
        _debug_print(f"Found detail page CSRF token: {detail_csrf_token[:20]}...")

        # Step 6: Perform approval action
        approve_url = urljoin(base_url, "/passed/")
        _debug_print(f"Performing approval at: {approve_url}")

        # Prepare approval data
        approval_data = {
            "workflow_id": str(workflow_id),
            "audit_remark": "运维组审批通过",  # 审批备注
            "csrfmiddlewaretoken": detail_csrf_token
        }

        # Set approval headers (表单提交，不是AJAX)
        approval_headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": detail_url,
            "X-CSRFToken": detail_csrf_token
        }

        # Perform approval request
        approval_response = session.post(
            approve_url,
            data=approval_data,
            headers=approval_headers,
            allow_redirects=True,  # 允许重定向
            timeout=timeout
        )

        _debug_print(f"Approval response status: {approval_response.status_code}")
        _debug_print(f"Final URL after approval: {approval_response.url}")
        _debug_print(f"Approval response content: {approval_response.text[:200]}...")

        if approval_response.status_code == 200:
            # 审批请求成功，现在需要验证审批结果
            # 等待一小段时间让系统处理审批
            import time
            time.sleep(2)

            # 重新获取工单状态来验证审批结果
            verification_result = get_sql_workflow_status_with_analysis(workflow_id, debug=debug)

            if verification_result["success"]:
                # 检查状态是否已经改变
                if verification_result["status_code"] == "PENDING_EXECUTION":
                    return {
                        "success": True,
                        "message": "工单审批通过成功",
                        "workflow_id": workflow_id,
                        "final_url": approval_response.url,
                        "new_status": verification_result["status"],
                        "new_status_code": verification_result["status_code"]
                    }
                elif verification_result["status_code"] == "PENDING_OPS_APPROVAL":
                    # 仍然是等待运维组审批，说明审批可能失败
                    return {
                        "success": False,
                        "error": "审批后工单仍处于等待运维组审批状态，审批可能失败",
                        "step": "approval_verification",
                        "workflow_id": workflow_id,
                        "final_url": approval_response.url,
                        "current_status": verification_result["status"]
                    }
                else:
                    # 状态发生了变化，但不是预期的状态
                    return {
                        "success": True,
                        "message": f"工单状态已变更为: {verification_result['status']}",
                        "workflow_id": workflow_id,
                        "final_url": approval_response.url,
                        "new_status": verification_result["status"],
                        "new_status_code": verification_result["status_code"]
                    }
            else:
                # 无法获取验证状态
                return {
                    "success": False,
                    "error": f"审批请求已发送，但无法验证结果: {verification_result.get('error')}",
                    "step": "approval_verification",
                    "workflow_id": workflow_id,
                    "final_url": approval_response.url
                }

        else:
            return {
                "success": False,
                "error": f"审批请求失败. Status code: {approval_response.status_code}",
                "step": "approval_action",
                "workflow_id": workflow_id
            }

    except requests.exceptions.Timeout:
        return {
            "success": False,
            "error": "Request timed out",
            "step": "network_timeout",
            "workflow_id": workflow_id
        }
    except requests.exceptions.ConnectionError as e:
        return {
            "success": False,
            "error": f"Connection error: {str(e)}",
            "step": "network_connection",
            "workflow_id": workflow_id
        }
    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "error": f"Network error: {str(e)}",
            "step": "network_error",
            "workflow_id": workflow_id
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"Unexpected error: {str(e)}",
            "step": "unexpected_error",
            "workflow_id": workflow_id
        }


def batch_approve_sql_workflows(workflow_urls, debug=False, max_workers=3):
    """
    批量审批多个SQL工单

    Args:
        workflow_urls (list): 工单URL列表
        debug (bool): 是否启用调试模式
        max_workers (int): 最大并发线程数，默认为3

    Returns:
        dict: 批量审批结果
    """
    from app.utils.wiki_utils import extract_workflow_id_from_sql_url

    results = {
        'success_count': 0,
        'failed_count': 0,
        'skipped_count': 0,
        'total_count': len(workflow_urls),
        'details': []
    }

    def process_single_workflow(workflow_url):
        """处理单个工单"""
        try:
            # 提取工单ID
            workflow_id = extract_workflow_id_from_sql_url(workflow_url)
            if not workflow_id:
                return {
                    'success': False,
                    'url': workflow_url,
                    'error': '无法从URL中提取工单ID',
                    'workflow_id': None,
                    'action': 'skipped'
                }

            # 首先检查工单状态
            status_result = get_sql_workflow_status_with_analysis(workflow_id, debug=debug)

            if not status_result['success']:
                return {
                    'success': False,
                    'url': workflow_url,
                    'workflow_id': workflow_id,
                    'error': f'无法获取工单状态: {status_result.get("error")}',
                    'action': 'skipped'
                }

            # 检查是否为等待运维组审批状态
            if status_result['status_code'] != 'PENDING_OPS_APPROVAL':
                return {
                    'success': False,
                    'url': workflow_url,
                    'workflow_id': workflow_id,
                    'error': f'工单状态不符合审批条件，当前状态: {status_result["status"]}',
                    'current_status': status_result['status'],
                    'status_code': status_result['status_code'],
                    'action': 'skipped'
                }

            # 检查是否包含指定的审批人信息
            extracted_text = status_result.get('extracted_text', '')
            required_approvers = ['徐世伟', '郭亚彬']
            has_required_approver = any(approver in extracted_text for approver in required_approvers)

            if not has_required_approver:
                return {
                    'success': False,
                    'url': workflow_url,
                    'workflow_id': workflow_id,
                    'error': '当前审批人不是徐世伟或郭亚彬，无法进行审批',
                    'current_status': status_result['status'],
                    'extracted_text': extracted_text,
                    'action': 'skipped'
                }

            # 执行审批
            approval_result = approve_sql_workflow(workflow_id, debug=debug)

            if approval_result['success']:
                return {
                    'success': True,
                    'url': workflow_url,
                    'workflow_id': workflow_id,
                    'message': '审批通过成功',
                    'action': 'approved'
                }
            else:
                return {
                    'success': False,
                    'url': workflow_url,
                    'workflow_id': workflow_id,
                    'error': approval_result.get('error'),
                    'action': 'failed'
                }

        except Exception as e:
            return {
                'success': False,
                'url': workflow_url,
                'workflow_id': None,
                'error': f'处理异常: {str(e)}',
                'action': 'failed'
            }

    # 使用线程池并发处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        process_results = list(executor.map(process_single_workflow, workflow_urls))

    # 统计结果
    for result in process_results:
        if result['success']:
            results['success_count'] += 1
        elif result.get('action') == 'skipped':
            results['skipped_count'] += 1
        else:
            results['failed_count'] += 1
        results['details'].append(result)

    return results


def get_pending_ops_approval_workflows(debug=False, max_workers=3, limit=14, offset=0,
                                      start_date="", end_date="", search=""):
    """
    获取待运维组审批的SQL工单列表

    Args:
        debug (bool): 是否启用调试模式
        max_workers (int): 最大并发线程数，默认为3，设为0则跳过详细状态检查
        limit (int): 每页显示数量，默认14
        offset (int): 偏移量，默认0
        start_date (str): 开始日期过滤，格式YYYY-MM-DD，默认为空
        end_date (str): 结束日期过滤，格式YYYY-MM-DD，默认为空
        search (str): 搜索关键词，默认为空

    Returns:
        dict: 包含待审批工单列表的结果

    Note:
        navStatus=workflow_manreviewing 表示所有待审批状态的工单
        需要进一步检查每个工单是否轮到运维组审批
    """
    def _debug_print(message):
        """Print debug message if debug mode is enabled"""
        if debug:
            print(f"🔧 [DEBUG] {message}")

    # Create session with proper headers
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "X-Requested-With": "XMLHttpRequest"
    })

    # Set timeout to prevent hanging
    timeout = 30
    base_url = SQL_PLATFORM_CONFIG['base_url']

    try:
        # Step 1: Get login page
        login_url = SQL_PLATFORM_CONFIG['login_url']
        _debug_print(f"Accessing login page: {login_url}")

        login_page_response = session.get(login_url, timeout=timeout)
        if login_page_response.status_code != 200:
            return {
                "success": False,
                "error": f"Failed to access login page: HTTP {login_page_response.status_code}",
                "workflows": []
            }

        # Step 2: Extract CSRF token
        _debug_print("Extracting CSRF token from login page...")
        csrf_match = re.search(r'name="csrfmiddlewaretoken" value="([^"]+)"', login_page_response.text)
        if not csrf_match:
            return {
                "success": False,
                "error": "CSRF token not found in login page",
                "workflows": []
            }

        csrf_token = csrf_match.group(1)
        _debug_print(f"Found CSRF token: {csrf_token[:20]}...")

        # 同时检查cookies中的csrftoken
        csrf_cookie = None
        for cookie in session.cookies:
            if cookie.name == 'csrftoken':
                csrf_cookie = cookie.value
                _debug_print(f"Found CSRF cookie: {csrf_cookie[:20]}...")
                break

        # Step 3: Perform authentication
        auth_url = urljoin(base_url, "/authenticate/")
        _debug_print(f"Performing authentication at: {auth_url}")

        # Prepare login data with CSRF token
        login_data = {
            "username": SQL_PLATFORM_CONFIG['username'],
            "password": SQL_PLATFORM_CONFIG['password'],
            "csrfmiddlewaretoken": csrf_token
        }

        auth_response = session.post(auth_url, data=login_data, timeout=timeout)
        if auth_response.status_code != 200:
            return {
                "success": False,
                "error": f"Authentication failed: HTTP {auth_response.status_code}",
                "workflows": []
            }

        _debug_print("Authentication successful")

        # Step 4: Access SQL workflow API with parameters
        workflow_api_url = urljoin(base_url, "/sqlworkflow_list/")

        # 构建GET参数（API不支持日期筛选，只传递基础参数）
        post_data = {
            'limit': 10000,  # 获取更多数据用于客户端筛选
            'offset': 0,
            'navStatus': 'workflow_manreviewing',  # 待审批状态（包含所有审批环节）
            'search': search
        }

        _debug_print(f"Accessing SQL workflow API: {workflow_api_url}")
        _debug_print(f"GET parameters: {post_data}")

        # 设置正确的请求头（GET请求不需要CSRF token）
        headers = {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "X-Requested-With": "XMLHttpRequest"
        }

        # 使用GET请求而不是POST请求
        api_response = session.get(workflow_api_url, params=post_data, headers=headers, timeout=timeout)
        if api_response.status_code != 200:
            return {
                "success": False,
                "error": f"Failed to access workflow API: HTTP {api_response.status_code}",
                "workflows": []
            }

        # Step 5: Parse JSON response
        _debug_print(f"API response status: {api_response.status_code}")
        _debug_print(f"API response headers: {dict(api_response.headers)}")
        _debug_print(f"API response content type: {api_response.headers.get('content-type', 'unknown')}")

        # 检查响应内容
        response_text = api_response.text
        _debug_print(f"API response length: {len(response_text)} characters")
        if debug:
            _debug_print(f"API response preview (first 500 chars): {response_text[:500]}")

        # 检查是否是HTML响应（可能是登录页面或错误页面）
        if response_text.strip().startswith('<!DOCTYPE') or response_text.strip().startswith('<html'):
            return {
                "success": False,
                "error": "API returned HTML instead of JSON, possibly authentication failed or session expired",
                "workflows": [],
                "response_preview": response_text[:200] if debug else None
            }

        # 检查是否是空响应
        if not response_text.strip():
            return {
                "success": False,
                "error": "API returned empty response",
                "workflows": []
            }

        try:
            api_data = api_response.json()
            _debug_print(f"API response parsed successfully")

            # 检查API响应格式
            if isinstance(api_data, dict):
                if 'data' in api_data:
                    _debug_print(f"API response received: {len(api_data.get('data', []))} workflows")
                else:
                    _debug_print(f"API response keys: {list(api_data.keys())}")
            else:
                _debug_print(f"API response type: {type(api_data)}")

        except json.JSONDecodeError as e:
            return {
                "success": False,
                "error": f"Failed to parse API response as JSON: {str(e)}",
                "workflows": [],
                "response_preview": response_text[:200] if debug else None,
                "content_type": api_response.headers.get('content-type', 'unknown')
            }

        # Step 6: Extract workflow information
        workflows = []

        # 检查API数据结构
        if isinstance(api_data, dict):
            # 尝试不同的数据结构
            workflow_data = None
            if 'data' in api_data:
                workflow_data = api_data['data']
            elif 'rows' in api_data:
                workflow_data = api_data['rows']
            elif 'results' in api_data:
                workflow_data = api_data['results']
            else:
                _debug_print(f"Unknown API data structure: {list(api_data.keys())}")
                return {
                    "success": False,
                    "error": f"Unknown API response structure. Available keys: {list(api_data.keys())}",
                    "workflows": []
                }
        elif isinstance(api_data, list):
            # API直接返回工单数组
            workflow_data = api_data
        else:
            return {
                "success": False,
                "error": f"Unexpected API response type: {type(api_data)}",
                "workflows": []
            }

        if not workflow_data:
            _debug_print("No workflow data found in API response")
            return {
                "success": True,
                "message": "成功获取到 0 个待运维组审批的工单",
                "total_found": 0,
                "pending_ops_approval": 0,
                "total_count": api_data.get('recordsTotal', 0) if isinstance(api_data, dict) else 0,
                "filtered_count": api_data.get('recordsFiltered', 0) if isinstance(api_data, dict) else 0,
                "workflows": []
            }

        _debug_print(f"Found {len(workflow_data)} items in API response")

        for i, item in enumerate(workflow_data):
            try:
                if not isinstance(item, dict):
                    _debug_print(f"Skipping non-dict item at index {i}: {type(item)}")
                    continue

                workflow_id = str(item.get('id', item.get('workflow_id', '')))
                if not workflow_id:
                    _debug_print(f"Skipping item without ID at index {i}")
                    continue

                workflow_url = f"{base_url}/detail/{workflow_id}/"

                workflow_info = {
                    'workflow_id': workflow_id,
                    'title': item.get('workflow_name', ''),
                    'submitter': item.get('engineer_display', ''),
                    'status': item.get('status', ''),
                    'submit_time': item.get('create_time', ''),
                    'url': workflow_url,
                    'db_name': item.get('db_name', ''),
                    'syntax_type': item.get('syntax_type', '')
                }
                workflows.append(workflow_info)

            except Exception as e:
                _debug_print(f"Error parsing workflow item at index {i}: {str(e)}")
                if debug:
                    _debug_print(f"Item content: {item}")
                continue

        _debug_print(f"Successfully parsed {len(workflows)} workflows from API")

        # Step 6: 客户端日期筛选（因为API不支持日期筛选）
        if start_date or end_date:
            _debug_print(f"Applying client-side date filtering: {start_date} to {end_date}")
            filtered_workflows = []

            for workflow in workflows:
                submit_time = workflow.get('submit_time', '')
                if submit_time:
                    try:
                        # 提取日期部分 (YYYY-MM-DD)
                        workflow_date = submit_time.split(' ')[0]

                        # 检查日期范围
                        include_workflow = True
                        if start_date and workflow_date < start_date:
                            include_workflow = False
                        if end_date and workflow_date > end_date:
                            include_workflow = False

                        if include_workflow:
                            filtered_workflows.append(workflow)
                    except Exception as e:
                        _debug_print(f"Error parsing date for workflow {workflow.get('workflow_id')}: {str(e)}")
                        # 如果日期解析失败，保留工单
                        filtered_workflows.append(workflow)
                else:
                    # 如果没有提交时间，保留工单
                    filtered_workflows.append(workflow)

            _debug_print(f"Date filtering: {len(workflows)} -> {len(filtered_workflows)} workflows")
            workflows = filtered_workflows

        # Step 6.5: 预过滤已完成的工单（在日期筛选后，减少后续详细状态检查的负担）
        if workflows:
            _debug_print("Applying pre-filter to skip obviously completed workflows...")
            pre_filtered_workflows = []
            skipped_count = 0

            for workflow in workflows:
                workflow_status = workflow.get('status', '')
                workflow_id = workflow.get('workflow_id', '')

                if workflow_status in ['workflow_finish', 'workflow_abort', 'workflow_exception']:
                    _debug_print(f"⏭️ 预过滤跳过工单 {workflow_id}: API状态为 {workflow_status}")
                    skipped_count += 1
                else:
                    pre_filtered_workflows.append(workflow)

            _debug_print(f"Pre-filtering: {len(workflows)} -> {len(pre_filtered_workflows)} workflows (跳过 {skipped_count} 个已完成工单)")
            workflows = pre_filtered_workflows

        # Step 7: 客户端分页处理
        total_workflows_after_date_filter = len(workflows)
        if offset > 0 or limit < len(workflows):
            _debug_print(f"Applying client-side pagination: offset={offset}, limit={limit}")
            start_idx = offset
            end_idx = offset + limit
            workflows = workflows[start_idx:end_idx]
            _debug_print(f"Pagination: showing {len(workflows)} workflows (from {start_idx} to {end_idx})")

        # Step 8: 并发查询每个工单的详细状态，筛选出轮到运维组审批的工单
        if workflows and max_workers > 0:
            _debug_print("Checking detailed status to filter workflows pending ops approval...")
            _debug_print(f"Found {len(workflows)} workflows with navStatus=workflow_manreviewing")

            def check_workflow_detail(workflow):
                """检查单个工单是否轮到运维组审批"""
                try:
                    workflow_id = workflow['workflow_id']
                    detail_result = get_sql_workflow_status_with_analysis(workflow_id, debug=False)
                    if detail_result['success']:
                        # 更新工单的详细状态信息
                        workflow.update({
                            'detailed_status': detail_result['status'],
                            'status_code': detail_result['status_code'],
                            'description': detail_result['description'],
                            'extracted_text': detail_result['extracted_text']
                        })

                        # 只返回真正轮到运维组审批的工单
                        if detail_result.get('status_code') == 'PENDING_OPS_APPROVAL':
                            _debug_print(f"✅ 工单 {workflow_id} 轮到运维组审批: {detail_result['status']}")
                            return workflow
                        else:
                            _debug_print(f"⏭️ 工单 {workflow_id} 不是运维组审批阶段: {detail_result['status']} ({detail_result.get('status_code')})")
                    else:
                        _debug_print(f"❌ 工单 {workflow_id} 状态检查失败: {detail_result.get('error')}")
                except Exception as e:
                    _debug_print(f"❌ 工单 {workflow.get('workflow_id')} 检查异常: {str(e)}")
                return None

            # 使用线程池并发检查
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                detailed_results = list(executor.map(check_workflow_detail, workflows))

            # 过滤出真正轮到运维组审批的工单
            pending_workflows = [w for w in detailed_results if w is not None]
            _debug_print(f"After filtering: {len(pending_workflows)} workflows are pending ops approval")
        else:
            # 如果不进行详细状态检查，直接返回API结果（可能包含非运维组审批的工单）
            _debug_print("Skipping detailed status check, returning all workflow_manreviewing workflows")
            pending_workflows = workflows

        # 计算统计信息
        api_total_count = len(workflow_data) if isinstance(workflow_data, list) else 0
        if isinstance(api_data, dict):
            api_total_count = api_data.get('total', api_total_count)

        # 使用客户端筛选后的数量
        date_filtered_count = total_workflows_after_date_filter if 'total_workflows_after_date_filter' in locals() else len(workflows)

        return {
            "success": True,
            "message": f"成功获取到 {len(pending_workflows)} 个待运维组审批的工单",
            "total_found": len(workflows),  # 当前页显示的工单数
            "pending_ops_approval": len(pending_workflows),
            "total_count": api_total_count,  # API返回的总数
            "date_filtered_count": date_filtered_count,  # 日期筛选后的总数
            "workflows": pending_workflows
        }

    except requests.exceptions.Timeout:
        return {
            "success": False,
            "error": "Request timeout while fetching workflow list",
            "workflows": []
        }
    except requests.exceptions.RequestException as e:
        return {
            "success": False,
            "error": f"Network error: {str(e)}",
            "workflows": []
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"Unexpected error: {str(e)}",
            "workflows": []
        }
    finally:
        session.close()



def test_sql_workflow_api_connection(debug=True):
    """
    测试SQL工单API连接和响应格式

    Args:
        debug (bool): 是否启用调试模式

    Returns:
        dict: 测试结果
    """
    def _debug_print(message):
        """Print debug message if debug mode is enabled"""
        if debug:
            print(f"🔧 [DEBUG] {message}")

    print("🧪 测试SQL工单API连接")
    print("=" * 60)

    # Create session with proper headers
    session = requests.Session()
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "X-Requested-With": "XMLHttpRequest"
    })

    timeout = 30
    base_url = SQL_PLATFORM_CONFIG['base_url']

    try:
        # Step 1: 测试登录
        print("🔐 测试登录...")
        login_url = SQL_PLATFORM_CONFIG['login_url']
        _debug_print(f"Login URL: {login_url}")

        login_page_response = session.get(login_url, timeout=timeout)
        print(f"   登录页面状态: {login_page_response.status_code}")

        if login_page_response.status_code != 200:
            return {"success": False, "error": f"无法访问登录页面: {login_page_response.status_code}"}

        # Extract CSRF token
        csrf_match = re.search(r'name="csrfmiddlewaretoken" value="([^"]+)"', login_page_response.text)
        if not csrf_match:
            return {"success": False, "error": "无法找到CSRF token"}

        csrf_token = csrf_match.group(1)
        print(f"   CSRF token: {csrf_token[:20]}...")

        # Perform authentication
        auth_url = urljoin(base_url, "/authenticate/")
        login_data = {
            "username": SQL_PLATFORM_CONFIG['username'],
            "password": SQL_PLATFORM_CONFIG['password'],
            "csrfmiddlewaretoken": csrf_token
        }

        auth_response = session.post(auth_url, data=login_data, timeout=timeout)
        print(f"   认证状态: {auth_response.status_code}")

        if auth_response.status_code != 200:
            return {"success": False, "error": f"认证失败: {auth_response.status_code}"}

        # Step 2: 测试API接口
        print("📡 测试API接口...")
        workflow_api_url = urljoin(base_url, "/sqlworkflow/")

        # 简单的测试参数
        params = {
            'limit': 5,
            'offset': 0,
            'navStatus': 'workflow_manreviewing'
        }

        _debug_print(f"API URL: {workflow_api_url}")
        _debug_print(f"Parameters: {params}")

        api_response = session.get(workflow_api_url, params=params, timeout=timeout)
        print(f"   API响应状态: {api_response.status_code}")
        print(f"   Content-Type: {api_response.headers.get('content-type', 'unknown')}")
        print(f"   响应长度: {len(api_response.text)} 字符")

        if api_response.status_code != 200:
            return {
                "success": False,
                "error": f"API请求失败: {api_response.status_code}",
                "response_preview": api_response.text[:200]
            }

        # 分析响应内容
        response_text = api_response.text
        print(f"   响应预览 (前200字符): {response_text[:200]}")

        if response_text.strip().startswith('<!DOCTYPE') or response_text.strip().startswith('<html'):
            return {
                "success": False,
                "error": "API返回HTML而不是JSON，可能是认证失败或会话过期",
                "response_preview": response_text[:500]
            }

        if not response_text.strip():
            return {"success": False, "error": "API返回空响应"}

        # 尝试解析JSON
        try:
            api_data = api_response.json()
            print(f"   JSON解析: 成功")
            print(f"   数据类型: {type(api_data)}")

            if isinstance(api_data, dict):
                print(f"   可用键: {list(api_data.keys())}")
                if 'data' in api_data:
                    data_items = api_data['data']
                    print(f"   数据项数量: {len(data_items) if isinstance(data_items, list) else 'N/A'}")
                    if isinstance(data_items, list) and len(data_items) > 0:
                        print(f"   第一项键: {list(data_items[0].keys()) if isinstance(data_items[0], dict) else 'N/A'}")

            return {
                "success": True,
                "message": "API连接测试成功",
                "api_data_structure": {
                    "type": str(type(api_data)),
                    "keys": list(api_data.keys()) if isinstance(api_data, dict) else None,
                    "data_count": len(api_data.get('data', [])) if isinstance(api_data, dict) and 'data' in api_data else None
                }
            }

        except json.JSONDecodeError as e:
            return {
                "success": False,
                "error": f"JSON解析失败: {str(e)}",
                "response_preview": response_text[:500]
            }

    except Exception as e:
        return {"success": False, "error": f"测试异常: {str(e)}"}
    finally:
        session.close()


def approve_single_sql_workflow_by_id(workflow_id, debug=False):
    """
    根据工单ID审批单个SQL工单（要求是待运维组审批状态）

    Args:
        workflow_id (int/str): 工单ID
        debug (bool): 是否启用调试模式

    Returns:
        dict: 审批结果
    """
    def _debug_print(message):
        """Print debug message if debug mode is enabled"""
        if debug:
            print(f"🔧 [DEBUG] {message}")

    try:
        # 首先检查工单状态
        _debug_print(f"Checking workflow {workflow_id} status...")
        status_result = get_sql_workflow_status_with_analysis(workflow_id, debug=debug)

        if not status_result['success']:
            return {
                'success': False,
                'workflow_id': workflow_id,
                'error': f'无法获取工单状态: {status_result.get("error")}',
                'action': 'failed'
            }

        # 检查是否为待运维组审批状态
        if status_result.get('status_code') != 'PENDING_OPS_APPROVAL':
            return {
                'success': False,
                'workflow_id': workflow_id,
                'error': f'工单状态不是待运维组审批，当前状态: {status_result.get("status")}',
                'current_status': status_result.get('status'),
                'status_code': status_result.get('status_code'),
                'action': 'skipped'
            }

        _debug_print(f"Workflow {workflow_id} is pending ops approval, proceeding with approval...")

        # 执行审批
        approval_result = approve_sql_workflow(workflow_id, debug=debug)

        if approval_result['success']:
            return {
                'success': True,
                'workflow_id': workflow_id,
                'message': '审批通过成功',
                'previous_status': status_result.get('status'),
                'action': 'approved'
            }
        else:
            return {
                'success': False,
                'workflow_id': workflow_id,
                'error': approval_result.get('error'),
                'action': 'failed'
            }

    except Exception as e:
        return {
            'success': False,
            'workflow_id': workflow_id,
            'error': f'处理异常: {str(e)}',
            'action': 'failed'
        }


if __name__ == "__main__":
    # 运行测试
    test_sql_workflow_status()

    # 示例：批量查询多个工单
    print("\n" + "=" * 60)
    print("🔄 批量查询示例")
    batch_result = batch_query_sql_workflows([6316, 6318,6287], debug=False)
    print(f"📊 查询结果: 成功 {batch_result['success_count']}/{batch_result['total_count']}")

    for detail in batch_result['details']:
        if detail['success']:
            print(f"✅ 工单 {detail['workflow_id']}: {detail['status']} - {detail['extracted_text']}")
        else:
            print(f"❌ 工单 {detail.get('workflow_id', 'Unknown')}: {detail['error']}")

    # 测试获取待审批工单列表
    print("\n" + "=" * 60)
    print("🔄 测试获取待运维组审批工单列表")
    pending_result = get_pending_ops_approval_workflows(debug=True)
    if pending_result['success']:
        print(f"📊 找到 {pending_result['pending_ops_approval']} 个待运维组审批的工单")
        for workflow in pending_result['workflows']:
            print(f"✅ 工单 {workflow['workflow_id']}: {workflow['title']} - {workflow.get('detailed_status', workflow['status'])}")
    else:
        print(f"❌ 获取失败: {pending_result['error']}")