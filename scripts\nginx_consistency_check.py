#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import shlex
from app import create_app, db
from app.models.nginx_check import NginxFileCheck
from concurrent.futures import ThreadPoolExecutor
import subprocess
import json
from datetime import datetime, timedelta
import logging
from logging.handlers import RotatingFileHandler

log_date = datetime.now().strftime('%Y-%m-%d')
log_file = f'/tmp/nginx_consistency_check.log_{log_date}'
log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
log_handler = RotatingFileHandler(log_file, maxBytes=10*1024*1024, backupCount=5)
log_handler.setFormatter(log_formatter)

logger = logging.getLogger('nginx_consistency_check')
logger.addHandler(log_handler)
logger.setLevel(logging.INFO)

# 定义需要检查的节点IP列表
NGINX_NODES = ['********', '********', '********', '********', '************', '************']

# 定义需要检查的目录列表
NGINX_DIRS = ['prod', 'tools', 'test', 'uat', 'sjk', 'oem', 'youfu', 'dev','common']

def check_file_consistency(node_ip, file_path):
    """检查单个文件在指定节点上的内容"""
    try:
        cmd = f'ssh -o ConnectTimeout=10 root@{node_ip} "cat {file_path} 2>/dev/null || echo \'\'"'
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=15
        )
        if result.returncode == 0:
            return {
                'node': node_ip,
                'content': result.stdout,
                'status': 'success'
            }
        else:
            return {
                'node': node_ip,
                'content': '',
                'status': 'error',
                'message': result.stderr.strip() if result.stderr else "未知错误"
            }
    except Exception as e:
        logger.error(f"检查节点 {node_ip} 的文件 {file_path} 失败: {str(e)}")
        return {
            'node': node_ip,
            'content': '',
            'status': 'error',
            'message': str(e)
        }

def get_config_files(node_ip, nginx_dir):
    """获取指定节点和目录下的所有配置文件"""
    try:
        remote_cmd = f'find /etc/nginx/{nginx_dir} -type f \\( -name \"*.conf\" -o -name \"*.ini\" \\) -print'
        safe_remote_cmd = shlex.quote(remote_cmd)
        cmd = f'ssh -o ConnectTimeout=15 root@{node_ip} {safe_remote_cmd}'
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=15
        )
        if result.returncode == 0:
            files = [f.strip() for f in result.stdout.strip().split('\n') if f.strip()]
            return files
        else:
            logging.warning(f"获取节点 {node_ip} 的目录 {nginx_dir} 下的配置文件失败: {result.stderr}")
            return []
    except Exception as e:
        logging.error(f"获取节点 {node_ip} 的目录 {nginx_dir} 下的配置文件失败: {str(e)}")
        return []

def process_files_consistency_check(file_paths):
    """处理多个文件的一致性检查"""
    try:
        for file_path in file_paths:
            try:
                # 确保文件路径是绝对路径
                if not file_path.startswith('/etc/nginx/'):
                    file_path = f'/etc/nginx/{file_path}'

                # 并行检查所有节点上的文件
                with ThreadPoolExecutor(max_workers=len(NGINX_NODES)) as executor:
                    results = list(executor.map(lambda n: check_file_consistency(n, file_path), NGINX_NODES))

                # 检查文件内容是否一致
                reference_node = NGINX_NODES[0]
                reference_content = next((r['content'] for r in results if r['node'] == reference_node), '')
                is_consistent = True
                nodes_content = []

                for result in results:
                    node_consistent = result['status'] == 'success' and result['content'] == reference_content
                    if not node_consistent:
                        is_consistent = False

                    nodes_content.append({
                        'node': result['node'],
                        'consistent': node_consistent,
                        'content': result['content'],
                        'status': result['status'],
                        'message': result.get('message', '')
                    })

                # 将检查结果保存到数据库
                check_result = {
                    'file_path': file_path,
                    'consistent': is_consistent,
                    'nodes': nodes_content
                }
                check_result_json = json.dumps(check_result)

                # 检查JSON结果的长度
                if len(check_result_json.encode('utf-8')) > 65535:  # MySQL TEXT字段的最大长度
                    logger.warning(f"文件 {file_path} 的检查结果超过数据库字段长度限制，跳过保存结果")
                    continue

                # 创建或更新数据库记录
                nginx_check = NginxFileCheck.query.filter_by(file_path=file_path).first()
                if nginx_check:
                    nginx_check.check_time = datetime.utcnow() + timedelta(hours=8)
                    nginx_check.is_consistent = is_consistent
                    nginx_check.check_result = check_result_json
                else:
                    nginx_check = NginxFileCheck(
                        file_path=file_path,
                        check_time=datetime.utcnow() + timedelta(hours=8),
                        is_consistent=is_consistent,
                        check_result=check_result_json
                    )
                    db.session.add(nginx_check)

                db.session.commit()
                logger.info(f"完成文件 {file_path} 的一致性检查并保存结果")

            except Exception as e:
                logger.error(f"处理文件 {file_path} 时发生错误: {str(e)}")
                continue

    except Exception as e:
        logger.error(f"处理文件一致性检查失败: {str(e)}")

def main():
    """主函数"""
    try:
        # 创建Flask应用实例
        app = create_app()
        # 使用应用上下文
        with app.app_context():
            # 获取所有需要检查的配置文件
            all_files = []
            reference_node = NGINX_NODES[0]      
            for nginx_dir in NGINX_DIRS:
                files = get_config_files(reference_node, nginx_dir)
                all_files.extend(files)
            if all_files:
                logger.info(f"找到 {len(all_files)} 个配置文件需要检查")
                process_files_consistency_check(all_files)
                logger.info("完成所有文件的一致性检查")
            else:
                logging.warning("未找到需要检查的配置文件")
    
    except Exception as e:
        logging.error(f"执行Nginx配置文件一致性检查失败: {str(e)}")

if __name__ == '__main__':
    main()