from flask import Blueprint, jsonify, request, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
import subprocess
from app.models.user import User
from app.utils.auth_utils import permission_required
from app.utils.wecom_utils import send_wecom_message

dbsync_bp = Blueprint('dbsync', __name__)

@dbsync_bp.route('/dbsync', methods=['POST'])
@jwt_required()
@permission_required('dbsync.sql_sync')
def sql_sync():
    # 获取当前用户信息
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    if not user:
        return jsonify({
            'success': False,
            'message': '未授权的访问',
            'code': 401
        }), 401

    # 获取并处理表名
    tables = request.json.get('tables')
    if not tables:
        return jsonify({
            'success': False,
            'message': '请提供需要同步的表名',
            'code': 400
        }), 400

    # 处理不同的输入格式
    if isinstance(tables, list):
        tables = ' '.join(tables)  # 如果是列表，将其转换为空格分隔的字符串
    elif isinstance(tables, str):
        tables = tables.replace(',', ' ')  # 如果是字符串，将逗号替换为空格
    else:
        return jsonify({
            'success': False,
            'message': '表名格式不正确',
            'code': 400
        }), 400

    try:
        # 执行远程shell脚本
        cmd = f'ssh -o ConnectTimeout=15 root@10.0.2.118 "/root/tools/SqlSync.sh deploy {tables}"'
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True,
            timeout=20
        )

        if result.returncode == 0:
            send_wecom_message(f"执行sale数据库 {tables} 表同步到易畅行的操作")
            
            return jsonify({
                'success': True,
                'message': '同步操作执行成功',
                'data': {
                    'tables': tables.split(),
                    'output': result.stdout.strip()
                },
                'code': 200
            })
        else:
            error_msg = result.stderr.strip() if result.stderr else "未知错误"
            return jsonify({
                'success': False,
                'message': f'同步操作执行失败: {error_msg}',
                'code': 500
            }), 500
                
    except subprocess.TimeoutExpired:
        return jsonify({
            'success': False,
            'message': '操作超时，请检查网络连接',
            'code': 408
        }), 408
    except Exception as e:
        current_app.logger.error(f"执行出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'系统错误: {str(e)}',
            'code': 500
        }), 500