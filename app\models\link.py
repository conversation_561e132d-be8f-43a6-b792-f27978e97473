from sqlalchemy import Column, Integer, String, Text, DateTime
from sqlalchemy.sql import func
from app.utils.db import Base, db

class Link(Base):
    __tablename__ = 'links'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(100), nullable=False)
    url = Column(String(255), nullable=False)
    category = Column(String(50), nullable=True)
    description = Column(Text, nullable=True)
    icon = Column(String(255), nullable=False, default='fa-link')
    created_at = Column(DateTime, nullable=False, default=func.current_timestamp())
    environment = Column(String(20), nullable=True, default='prod')
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'url': self.url,
            'category': self.category,
            'description': self.description,
            'icon': self.icon,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            'environment': self.environment
        }