import subprocess
import json
from flask import current_app
from app.utils.wecom_utils import send_wecom_message

def get_fail2ban_data():
    """
    获取所有被封禁的IP地址
    """
    try:
        cmd_ips = f'ssh -o ConnectTimeout=10 root@******** "python3 /root/script/fail2ban.py get_banned_ips"'
        ips_result = subprocess.run(cmd_ips, shell=True, capture_output=True, text=True, timeout=10)

        if ips_result.returncode == 0:
            data = json.loads(ips_result.stdout)
            # 确保数据格式正确
            if isinstance(data, list):
                return {"banned_ips": data}
            elif isinstance(data, dict) and "banned_ips" in data:
                return data
            return {"banned_ips": []}
        else:
            current_app.logger.error(f"获取封禁IP失败: {ips_result.stderr}")
            return {"banned_ips": []}
    except Exception as e:
        current_app.logger.error(f"获取封禁IP数据时发生错误: {str(e)}")
        return {"banned_ips": []}

def get_enabled_jails():
    """
    获取所有启用的jail
    """
    try:
        cmd = f'ssh -o ConnectTimeout=10 root@******** "python3 /root/script/fail2ban.py get_enabled_jails"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            # 确保返回的是字典格式，包含jails键
            data = json.loads(result.stdout)
            if isinstance(data, list):
                return {"jails": data}
            return data
        else:
            current_app.logger.error(f"获取监狱列表失败: {result.stderr}")
            return {"jails": []}
    except Exception as e:
        current_app.logger.error(f"获取监狱列表时发生错误: {str(e)}")
        return {"jails": []}

def ban_ip(ip, jail):
    """
    手动封禁IP
    """
    try:
        cmd = f'ssh -o ConnectTimeout=10 root@******** "fail2ban-client set {jail} banip {ip}"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

        success = result.returncode == 0
        if success:
            send_wecom_message(f"手动封禁IP：{ip}，监狱：{jail}")
            current_app.logger.info(f"成功封禁IP {ip} 在监狱 {jail}")
        else:
            error_msg = result.stderr.strip() if result.stderr else "未知错误"
            current_app.logger.error(f"封禁IP失败: {error_msg}")
        return success
    except Exception as e:
        current_app.logger.error(f"封禁IP时发生错误: {str(e)}")
        return False

def unban_ip(ip, jail):
    try:
        cmd = f'ssh -o ConnectTimeout=10 root@******** "fail2ban-client set {jail} unbanip {ip}"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

        success = result.returncode == 0
        if success:
            send_wecom_message(f"手动解封IP：{ip}，监狱：{jail}")
            current_app.logger.info(f"成功解封IP {ip} 在监狱 {jail}")
        else:
            error_msg = result.stderr.strip() if result.stderr else "未知错误"
            current_app.logger.error(f"解封IP失败: {error_msg}")
        return success
    except Exception as e:
        current_app.logger.error(f"解封IP时发生错误: {str(e)}")
        return False 