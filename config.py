import os

# 基础配置
SECRET_KEY = 'nihao123.'
MAX_CAPTCHA_ATTEMPTS = 5

# 数据库配置
MYSQL_HOST = '**********'
MYSQL_USER = 'yunwei'
MYSQL_PASSWORD = 'da1Biao2ge!'
MYSQL_DB = 'iyunwei'

# SQLAlchemy 数据库 URL - 为 MySQL 5.7 添加正确的字符集和连接参数
SQLALCHEMY_DATABASE_URI = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}/{MYSQL_DB}?charset=utf8mb4"
SQLALCHEMY_TRACK_MODIFICATIONS = False
SQLALCHEMY_POOL_RECYCLE = 3600
SQLALCHEMY_POOL_SIZE = 10
SQLALCHEMY_MAX_OVERFLOW = 5
SQLALCHEMY_POOL_TIMEOUT = 30

# 原来的数据库连接配置（如果其他地方还在使用）
DB_CONFIG = {
    'host': MYSQL_HOST,
    'user': MYSQL_USER,
    'password': MYSQL_PASSWORD,
    'db': MYSQL_DB,
    'charset': 'utf8mb4'
}

# 企业微信配置
WECOM_WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4840fac6-cd03-4d71-a490-fa55a3d4d03a"

# Wiki系统配置
WIKI_CONFIG = {
    'base_url': 'https://wiki.serviceshare.com',
    'login_url': 'https://wiki.serviceshare.com/doku.php?do=login',
    'username': 'guoyabin',
    'password': 'zx4682354'
}

# SQL平台(Archery)配置
SQL_PLATFORM_CONFIG = {
    'base_url': 'https://sql.serviceshare.com',
    'login_url': 'https://sql.serviceshare.com/login/',
    'username': 'guoyabin',
    'password': 'zx4682354'
}

# Apollo配置管理系统配置
APOLLO_CONFIG = {
    'base_url': 'https://apollo.serviceshare.com',
    'api_base_url': 'https://apollo.serviceshare.com/openapi/v1',
    'login_url': 'https://apollo.serviceshare.com/signin',
    'username': 'apollo',
    'password': 'shenbianyun_2021@@',
    'api_token': '6a6757b4300c0a5b63a472605d78b21a6a3b5ae09011480db5c0e4524e7229e0'
}

# GitLab配置
GITLAB_CONFIG = {
    'base_url': 'http://***********:58880',
    'api_base_url': 'http://***********:58880/api/v4',
    'api_token': '**************************',
    # 地址映射配置：内网地址 -> 外网地址
    'address_mapping': {
        'internal_address': '**************',
        'external_address': '***********:58880',
        'internal_base_url': 'http://**************',
        'external_base_url': 'http://***********:58880'
    }
}

# JWT 配置
JWT_SECRET_KEY = "haoni213."
JWT_TOKEN_LOCATION = ["headers"]
JWT_ACCESS_TOKEN_EXPIRES = 3600
JWT_HEADER_NAME = "Authorization"
JWT_HEADER_TYPE = "Bearer"
JWT_ALGORITHM = "HS256"

# 应用配置
DEBUG = True

class Config:
    DATABASE = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'database.db') 