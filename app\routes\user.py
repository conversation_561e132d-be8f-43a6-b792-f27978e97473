from flask import Flask, jsonify, Blueprint, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.user import User

user_bp = Blueprint('user', __name__)

@user_bp.route('/user/info', methods=['GET'])
@jwt_required()
def get_user_info():
    try:
        # 从JWT中获取用户身份
        user_id = get_jwt_identity()

        # 查询用户信息
        user = User.query.get(user_id)
        if not user:
            return jsonify({'code': 404, 'message': '用户不存在'}), 404

        # 构建响应数据
        return jsonify({
            'code': 200,
            'message': 'success',
            'data': {
                'username': user.username,
                'lastLoginTime': user.last_login_time.strftime("%Y-%m-%d %H:%M:%S") if user.last_login_time else None
            }
        })
    except Exception as e:
        current_app.logger.error(f"服务器内部错误: {str(e)}")
        return jsonify({'code': 500, 'message': '服务器内部错误'}), 500

def register_template_filters(app):
    @app.template_filter("strftime")
    def _jinja2_filter_datetime(date, fmt="%Y-%m-%d %H:%M:%S"):
        return date.strftime(fmt) if date else ""