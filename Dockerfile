# 使用官方 Python 镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN echo "deb http://mirrors.aliyun.com/debian/ stable main contrib non-free" > /etc/apt/sources.list \
    && echo "deb-src http://mirrors.aliyun.com/debian/ stable main contrib non-free" >> /etc/apt/sources.list \
    && apt-get update \
    && apt-get install -y \
        openssh-client \
        wget \
        unzip \
        libaio1 \
    && rm -rf /var/lib/apt/lists/*

# 安装 Oracle Instant Client
RUN mkdir -p /opt/oracle && \
    cd /opt/oracle && \
    wget https://download.oracle.com/otn_software/linux/instantclient/1912000/instantclient-basic-linux.x64-*********.0dbru.zip && \
    unzip instantclient-basic-linux.x64-*********.0dbru.zip && \
    rm instantclient-basic-linux.x64-*********.0dbru.zip && \
    mv instantclient_19_12 instantclient && \
    echo /opt/oracle/instantclient > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig

# 设置 Oracle 环境变量
ENV LD_LIBRARY_PATH=/opt/oracle/instantclient:$LD_LIBRARY_PATH
ENV ORACLE_HOME=/opt/oracle/instantclient

# 复制 requirements.txt 先安装依赖（利用 Docker 缓存）
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 设置环境变量
ENV FLASK_APP=app.py
ENV PYTHONPATH=/app

# 暴露应用端口（根据 app.py 中的配置，应用运行在 5555 端口）
EXPOSE 5555

# 创建启动脚本
RUN echo '#!/bin/bash\n\
export LD_LIBRARY_PATH=/opt/oracle/instantclient:$LD_LIBRARY_PATH\n\
export ORACLE_HOME=/opt/oracle/instantclient\n\
cd /app\n\
python app.py' > /app/start.sh && \
    chmod +x /app/start.sh

# 运行应用
CMD ["/app/start.sh"]
