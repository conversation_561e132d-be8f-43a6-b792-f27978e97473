# 使用官方 Python 镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN echo "deb http://mirrors.aliyun.com/debian/ stable main contrib non-free" > /etc/apt/sources.list \
    && echo "deb-src http://mirrors.aliyun.com/debian/ stable main contrib non-free" >> /etc/apt/sources.list \
    && apt-get update \
    && apt-get install -y \
        openssh-client \
        wget \
        unzip \
        libaio1 \
    && rm -rf /var/lib/apt/lists/*

# 创建Oracle目录并设置动态链接库配置
RUN mkdir -p /opt/oracle && \
    echo /opt/oracle/instantclient_11_2 > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig

# 设置 Oracle 环境变量
ENV LD_LIBRARY_PATH=/opt/oracle/instantclient_11_2:$LD_LIBRARY_PATH
ENV ORACLE_HOME=/opt/oracle/instantclient_11_2

# 复制 requirements.txt 先安装依赖（利用 Docker 缓存）
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制启动脚本
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# 复制应用代码
COPY . .

# 设置环境变量
ENV FLASK_APP=app.py
ENV PYTHONPATH=/app

# 暴露应用端口（根据 app.py 中的配置，应用运行在 5555 端口）
EXPOSE 5555

# 运行应用
ENTRYPOINT ["docker-entrypoint.sh"]
