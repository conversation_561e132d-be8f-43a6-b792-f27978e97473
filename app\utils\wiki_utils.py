import requests
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor
import re
from urllib.parse import urljoin, urlparse

import logging

# 导入配置

from config import WIKI_CONFIG, APOLLO_CONFIG


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def login():
    """登录wiki系统"""
    # 从配置文件获取登录信息
    login_url = "https://wiki.serviceshare.com/dologin.action"
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Cookie": "JSESSIONID=B977D71F2F29D633BA0621E614B7F9BD",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36"
    }
    data = {
        "os_username": WIKI_CONFIG['username'],
        "os_password": WIKI_CONFIG['password'],
        "login": "登录",
        "os_destination": ""
    }

    try:
        s = requests.session()
        s.timeout = 30
        response = s.post(url=login_url, headers=headers, data=data, timeout=30)

        if response.status_code == 200:
            logger.info("Wiki登录成功")
            return s
        else:
            logger.error(f"Wiki登录失败，状态码: {response.status_code}")
            return None

    except requests.exceptions.Timeout:
        logger.error("Wiki登录超时")
        return None
    except Exception as e:
        logger.error(f"Wiki登录异常: {str(e)}")
        return None

def search_articles(session):
    """搜索wiki文章"""
    # 获取北京时间今天的日期
    from datetime import datetime, timezone, timedelta
    beijing_tz = timezone(timedelta(hours=8))
    today = datetime.now(beijing_tz).strftime("%Y%m%d")

    search_url = "https://wiki.serviceshare.com/rest/api/search"
    params = {
        "cql": f"siteSearch ~ \"{today}-\" AND type in (\"page\")",
        "start": 0,
        "limit": 5
    }

    try:
        response = session.get(search_url, params=params, timeout=30)

        if response.status_code == 200:
            data = response.json()
            total_results = len(data.get('results', []))
            logger.info(f"找到 {total_results} 个搜索结果")

            articles = []
            for result in data.get('results', []):
                title = result.get('content', {}).get('title', '')
                if '上线工单' in title:
                    webui_link = result.get('content', {}).get('_links', {}).get('webui')
                    articles.append(webui_link)

            logger.info(f"找到 {len(articles)} 个上线工单")
            return articles
        else:
            logger.error(f"搜索失败，状态码: {response.status_code}")
            return None

    except requests.exceptions.Timeout:
        logger.error("搜索超时")
        return None
    except Exception as err:
        logger.error(f"搜索异常: {err}")
        return None

def fetch_article_links(session, article_url):
    """
    抓取文章中的链接并根据域名进行分类
    """
    try:
        full_url = urljoin("https://wiki.serviceshare.com", article_url)
        response = session.get(full_url, timeout=30)

        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            links = soup.find_all('a', href=True)

            git_links = []
            sql_links = []
            apollo_links = []

            for link in links:
                href = link['href']

                # 检查Git审批链接 - 支持内网和外网地址
                internal_pattern = r'^https?://192\.168\.40\.191[^?]*/merge_requests/\d+'
                external_pattern = r'^https?://43\.254\.89\.5:58880[^?]*/merge_requests/\d+'
                if re.match(internal_pattern, href) or re.match(external_pattern, href):
                    git_links.append(href)

                # 检查SQL审批链接
                elif href.startswith('http://sql.serviceshare.com') or href.startswith('https://sql.serviceshare.com'):
                    sql_links.append(href)

                # 检查Apollo审批链接
                elif href.startswith('http://apollo.serviceshare.com') or href.startswith('https://apollo.serviceshare.com'):
                    apollo_links.append(href)

            total_found = len(git_links) + len(sql_links) + len(apollo_links)
            if total_found > 0:
                logger.info(f"文章链接分类完成: Git {len(git_links)}, SQL {len(sql_links)}, Apollo {len(apollo_links)}")

            return {
                'git_links': git_links,
                'sql_links': sql_links,
                'apollo_links': apollo_links
            }
        else:
            logger.error(f"获取文章失败，状态码: {response.status_code}")

    except requests.exceptions.Timeout:
        logger.error(f"获取文章超时: {article_url}")
    except Exception as e:
        logger.error(f"抓取文章链接时出错: {e}")

    return {
        'git_links': [],
        'sql_links': [],
        'apollo_links': []
    }

def process_articles(session, articles):
    """
    多线程处理文章列表，抓取并分类链接
    """
    logger.info(f"开始处理 {len(articles)} 个文章")

    all_git_links = []
    all_sql_links = []
    all_apollo_links = []

    def process_article(article_url):
        return fetch_article_links(session, article_url)

    # 使用线程池并发处理文章
    with ThreadPoolExecutor(max_workers=3) as executor:
        results = list(executor.map(lambda url: process_article(url), articles))

    # 合并结果
    for result in results:
        all_git_links.extend(result['git_links'])
        all_sql_links.extend(result['sql_links'])
        all_apollo_links.extend(result['apollo_links'])

    # 去重
    all_git_links = list(set(all_git_links))
    all_sql_links = list(set(all_sql_links))
    all_apollo_links = list(set(all_apollo_links))

    total_links = len(all_git_links) + len(all_sql_links) + len(all_apollo_links)
    if total_links > 0:
        logger.info(f"文章处理完成: Git {len(all_git_links)}, SQL {len(all_sql_links)}, Apollo {len(all_apollo_links)}")

    return {
        'git_links': all_git_links,
        'sql_links': all_sql_links,
        'apollo_links': all_apollo_links
    }

def analyze_status_text(text):
    """
    分析状态文本并返回对应的状态信息

    Args:
        text (str): 状态文本

    Returns:
        dict: 状态信息
    """
    text = text.strip()

    # 状态匹配规则 - 根据用户需求更新的精确匹配
    if "等待审批" in text and "管理组->运维组" in text:
        return {
            "status": "等待部门主管审批",
            "description": "工单正在等待部门主管审批",
            "raw_text": text,
            "status_code": "PENDING_MANAGER_APPROVAL"
        }

    elif "执行结果：执行有异常" in text:
        return {
            "status": "SQL执行错误",
            "description": "SQL执行过程中出现异常",
            "raw_text": text,
            "status_code": "SQL_EXECUTION_ERROR"
        }

    elif "下级审批：运维组" in text:
        return {
            "status": "等待运维组审批",
            "description": "工单正在等待运维组审批",
            "raw_text": text,
            "status_code": "PENDING_OPS_APPROVAL"
        }

    elif "下级审批：None" in text:
        return {
            "status": "等待执行",
            "description": "工单审批完成，等待执行",
            "raw_text": text,
            "status_code": "PENDING_EXECUTION"
        }

    elif "执行结果：已正常结束" in text:
        return {
            "status": "执行完成",
            "description": "工单已成功执行完成",
            "raw_text": text,
            "status_code": "COMPLETED_SUCCESSFULLY"
        }

    # 未匹配到已知状态
    return {
        "status": "未知",
        "description": "未识别的状态",
        "raw_text": text,
        "status_code": "UNKNOWN"
    }

def extract_links_from_wiki_url(wiki_url):
    """
    从指定的wiki页面URL中提取SQL、Apollo、Git链接

    Args:
        wiki_url (str): wiki页面URL

    Returns:
        dict: 包含分类链接的字典
    """
    try:
        session = login()
        if not session:
            logger.error("登录wiki失败")
            return {
                'success': False,
                'error': '登录wiki失败',
                'git_links': [],
                'sql_links': [],
                'apollo_links': []
            }

        # 获取页面内容
        response = session.get(wiki_url, timeout=30)

        if response.status_code != 200:
            logger.error(f"获取wiki页面失败，状态码: {response.status_code}")
            return {
                'success': False,
                'error': f'获取wiki页面失败，状态码: {response.status_code}',
                'git_links': [],
                'sql_links': [],
                'apollo_links': []
            }

        # 解析页面内容
        soup = BeautifulSoup(response.text, 'html.parser')
        links = soup.find_all('a', href=True)

        git_links = []
        sql_links = []
        apollo_links = []

        for link in links:
            href = link['href']

            # 检查Git审批链接 - 支持内网和外网地址
            internal_pattern = r'^https?://192\.168\.40\.191[^?]*/merge_requests/\d+'
            external_pattern = r'^https?://43\.254\.89\.5:58880[^?]*/merge_requests/\d+'
            if re.match(internal_pattern, href) or re.match(external_pattern, href):
                git_links.append(href)

            # 检查SQL审批链接
            elif href.startswith('http://sql.serviceshare.com') or href.startswith('https://sql.serviceshare.com'):
                sql_links.append(href)

            # 检查Apollo审批链接
            elif href.startswith('http://apollo.serviceshare.com') or href.startswith('https://apollo.serviceshare.com'):
                apollo_links.append(href)

        # 去重
        git_links = list(set(git_links))
        sql_links = list(set(sql_links))
        apollo_links = list(set(apollo_links))

        total_found = len(git_links) + len(sql_links) + len(apollo_links)
        if total_found > 0:
            logger.info(f"链接提取完成: Git {len(git_links)}, SQL {len(sql_links)}, Apollo {len(apollo_links)}")

        return {
            'success': True,
            'git_links': git_links,
            'sql_links': sql_links,
            'apollo_links': apollo_links
        }

    except requests.exceptions.Timeout:
        logger.error("提取链接超时")
        return {
            'success': False,
            'error': '请求超时',
            'git_links': [],
            'sql_links': [],
            'apollo_links': []
        }
    except Exception as e:
        logger.error(f"提取链接时出错: {str(e)}")
        return {
            'success': False,
            'error': f'提取链接时出错: {str(e)}',
            'git_links': [],
            'sql_links': [],
            'apollo_links': []
        }

def extract_workflow_id_from_sql_url(sql_url):
    """
    从SQL平台URL中提取工单ID

    Args:
        sql_url (str): SQL平台URL

    Returns:
        int or None: 工单ID
    """
    try:
        # 匹配URL中的工单ID，例如：http://sql.serviceshare.com/detail/6318/
        pattern = r'/detail/(\d+)/?'
        match = re.search(pattern, sql_url)
        if match:
            return int(match.group(1))
    except Exception:
        pass
    return None

def check_git_status(git_url):
    """
    检查Git合并请求状态

    Args:
        git_url (str): Git合并请求URL

    Returns:
        dict: 状态信息
    """
    try:
        # 导入GitLab检查工具
        from app.utils.gitlab_utils import check_gitlab_merge_request_status

        # 使用GitLab API检查合并请求状态
        result = check_gitlab_merge_request_status(git_url, debug=False)

        if result.get('success', False):
            # 成功获取状态
            return {
                'success': True,
                'url': git_url,
                'status': result.get('status', '未知状态'),
                'description': result.get('description', 'GitLab合并请求状态检查完成'),
                'status_code': result.get('status_code', 'UNKNOWN'),
                'title': result.get('title', ''),
                'author': result.get('author', ''),
                'source_branch': result.get('source_branch', ''),
                'target_branch': result.get('target_branch', ''),
                'created_at': result.get('created_at', ''),
                'updated_at': result.get('updated_at', ''),
                'merged_at': result.get('merged_at', ''),
                'project_path': result.get('project_path', ''),
                'merge_request_iid': result.get('merge_request_iid', ''),
                'check_time': result.get('check_time', '')
            }
        else:
            # 检查失败，返回错误信息
            error_msg = result.get('error', '未知错误')
            logger.error(f"GitLab状态检查失败: {git_url}, 错误: {error_msg}")

            return {
                'success': False,
                'url': git_url,
                'error': f'检查GitLab状态失败: {error_msg}',
                'status': result.get('status', '检查失败'),
                'description': result.get('description', '无法获取GitLab状态'),
                'status_code': result.get('status_code', 'CHECK_FAILED')
            }

    except Exception as e:
        logger.error(f"Git状态检查失败: {git_url}, 错误: {str(e)}")
        return {
            'success': False,
            'url': git_url,
            'error': f'检查Git状态失败: {str(e)}',
            'status': '检查失败',
            'description': '无法获取Git状态',
            'status_code': 'CHECK_FAILED'
        }

def check_apollo_status(apollo_url, api_token=None):
    """
    检查Apollo配置中心状态

    Args:
        apollo_url (str): Apollo配置中心URL
        api_token (str): Apollo Open API Token

    Returns:
        dict: 状态信息
    """
    try:
        # 导入Apollo检查工具
        from app.utils.apollo_utils import check_apollo_config_status

        # 如果没有提供API Token，尝试使用配置文件中的默认token
        if not api_token:
            api_token = APOLLO_CONFIG.get('api_token')

        if not api_token:
            logger.warning(f"未提供Apollo API Token且配置文件中未设置默认token，无法自动检查: {apollo_url}")
            return {
                'success': True,
                'url': apollo_url,
                'status': '需要手动检查',
                'description': 'Apollo配置状态需要手动检查（未提供API Token）',
                'status_code': 'MANUAL_CHECK_REQUIRED'
            }

        # 使用新的Apollo API检查功能 (关闭调试模式)
        result = check_apollo_config_status(apollo_url, debug=False, token=api_token)

        if result.get('success', False):
            has_pending = result.get('has_pending_release', False)
            status = '有待发布配置' if has_pending else '无待发布配置'
            status_code = 'PENDING_RELEASE' if has_pending else 'NO_PENDING_RELEASE'

            return {
                'success': True,
                'url': apollo_url,
                'status': status,
                'description': f'Apollo配置检查完成 - {status}',
                'status_code': status_code,
                'appid': result.get('appid', ''),
                'env': result.get('env', ''),
                'cluster': result.get('cluster', ''),
                'namespace': result.get('namespace', ''),
                'has_pending_release': has_pending,
                'check_time': result.get('check_time', ''),
                'status_info': result.get('status_info', {})
            }
        else:
            error_msg = result.get('error', '未知错误')
            logger.error(f"Apollo状态检查失败: {apollo_url}, 错误: {error_msg}")

            return {
                'success': False,
                'url': apollo_url,
                'error': f'检查Apollo状态失败: {error_msg}',
                'status': '检查失败',
                'description': '无法获取Apollo状态',
                'status_code': 'CHECK_FAILED'
            }

    except Exception as e:
        logger.error(f"Apollo状态检查失败: {apollo_url}, 错误: {str(e)}")
        return {
            'success': False,
            'url': apollo_url,
            'error': f'检查Apollo状态失败: {str(e)}',
            'status': '检查失败',
            'description': '无法获取Apollo状态',
            'status_code': 'CHECK_FAILED'
        }

def check_all_approval_status(git_links, sql_links, apollo_links, max_workers=3, compact=False):
    """
    并发检查所有审批链接的状态

    Args:
        git_links (list): Git链接列表
        sql_links (list): SQL链接列表
        apollo_links (list): Apollo链接列表
        max_workers (int): 最大并发线程数
        compact (bool): 是否返回精简格式

    Returns:
        dict: 所有状态检查结果
    """
    total_links = len(git_links) + len(sql_links) + len(apollo_links)
    if total_links > 0:
        logger.info(f"开始检查 {total_links} 个审批链接状态")

    results = {
        'git_results': [],
        'sql_results': [],
        'apollo_results': [],
        'summary': {
            'total_count': total_links,
            'git_count': len(git_links),
            'sql_count': len(sql_links),
            'apollo_count': len(apollo_links)
        }
    }

    def check_sql_workflow(sql_url):
        """检查SQL工单状态"""
        try:
            from app.utils.archery_utils import get_sql_workflow_status_with_analysis
            workflow_id = extract_workflow_id_from_sql_url(sql_url)
            if workflow_id:
                result = get_sql_workflow_status_with_analysis(workflow_id, debug=False)
                result['url'] = sql_url
                return result
            else:
                logger.error(f"无法从URL提取工单ID: {sql_url}")
                return {
                    'success': False,
                    'url': sql_url,
                    'error': '无法从URL中提取工单ID',
                    'status': '解析失败',
                    'description': '无法解析SQL工单ID',
                    'status_code': 'PARSE_FAILED'
                }
        except Exception as e:
            logger.error(f"SQL工单状态检查异常: {sql_url}, 错误: {str(e)}")
            return {
                'success': False,
                'url': sql_url,
                'error': f'检查异常: {str(e)}',
                'status': '检查失败',
                'description': '状态检查过程中出现异常',
                'status_code': 'CHECK_FAILED'
            }

    # 使用线程池并发检查状态
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 检查Git状态
        if git_links:
            git_futures = [executor.submit(check_git_status, url) for url in git_links]
            results['git_results'] = [future.result() for future in git_futures]

        # 检查SQL状态
        if sql_links:
            sql_futures = [executor.submit(check_sql_workflow, url) for url in sql_links]
            results['sql_results'] = [future.result() for future in sql_futures]

        # 检查Apollo状态
        if apollo_links:
            apollo_futures = [executor.submit(check_apollo_status, url) for url in apollo_links]
            results['apollo_results'] = [future.result() for future in apollo_futures]

    # 统计成功/失败数量
    git_success = sum(1 for r in results['git_results'] if r.get('success', False))
    sql_success = sum(1 for r in results['sql_results'] if r.get('success', False))
    apollo_success = sum(1 for r in results['apollo_results'] if r.get('success', False))

    if total_links > 0:
        logger.info(f"状态检查完成: Git {git_success}/{len(git_links)}, SQL {sql_success}/{len(sql_links)}, Apollo {apollo_success}/{len(apollo_links)}")

    # 如果需要精简格式，只返回核心信息
    if compact:
        return _format_compact_results(results)

    return results

def _format_compact_results(results):
    """
    将详细的状态检查结果转换为精简格式

    Args:
        results (dict): 详细的状态检查结果

    Returns:
        dict: 精简格式的结果
    """
    compact_results = {
        'apollo_results': [],
        'git_results': [],
        'sql_results': [],
        'summary': {
            'apollo_count': results['summary']['apollo_count'],
            'git_count': results['summary']['git_count'],
            'sql_count': results['summary']['sql_count'],
            'total_count': results['summary']['total_count']
        }
    }

    # 精简Apollo结果
    for apollo in results['apollo_results']:
        compact_apollo = {
            'appid': apollo.get('appid', ''),
            'env': apollo.get('env', ''),
            'cluster': apollo.get('cluster', ''),
            'status': apollo.get('status', '未知'),
            'status_code': apollo.get('status_code', 'UNKNOWN'),
            'description': apollo.get('description', ''),
            'check_time': apollo.get('check_time', ''),
            'url': apollo.get('url', '')
        }
        compact_results['apollo_results'].append(compact_apollo)

    # 精简Git结果
    for git in results['git_results']:
        compact_git = {
            'title': git.get('title', ''),
            'author': git.get('author', ''),
            'status': git.get('status', '未知'),
            'status_code': git.get('status_code', 'UNKNOWN'),
            'source_branch': git.get('source_branch', ''),
            'target_branch': git.get('target_branch', ''),
            'merged_at': git.get('merged_at', ''),
            'url': git.get('url', '')
        }
        compact_results['git_results'].append(compact_git)

    # 精简SQL结果
    for sql in results['sql_results']:
        compact_sql = {
            'workflow_id': sql.get('workflow_id', ''),
            'status': sql.get('status', '未知'),
            'status_code': sql.get('status_code', 'UNKNOWN'),
            'description': sql.get('description', ''),
            'suggestion': sql.get('suggestion', ''),
            'url': sql.get('url', '')
        }
        compact_results['sql_results'].append(compact_sql)

    return compact_results

def process_wiki_request(wiki_url=None, compact=False):
    """
    处理wiki请求的主函数

    Args:
        wiki_url (str, optional): wiki页面URL，如果为空则自动搜索
        compact (bool): 是否返回精简格式

    Returns:
        dict: 处理结果
    """
    try:
        if wiki_url:
            # 从指定URL提取链接
            link_result = extract_links_from_wiki_url(wiki_url)
            if not link_result['success']:
                logger.error("从指定URL提取链接失败")
                return {
                    'success': False,
                    'error': link_result['error'],
                    'data': None
                }

            git_links = link_result['git_links']
            sql_links = link_result['sql_links']
            apollo_links = link_result['apollo_links']
            source = 'specified_url'

        else:
            session = login()
            if not session:
                logger.error("自动登录失败")
                return {
                    'success': False,
                    'error': '登录wiki失败',
                    'data': None
                }

            articles = search_articles(session)
            if not articles:
                logger.error("未找到符合条件的文章")
                return {
                    'success': False,
                    'error': '未找到符合条件的文章',
                    'data': None
                }

            links = process_articles(session, articles)
            git_links = links['git_links']
            sql_links = links['sql_links']
            apollo_links = links['apollo_links']
            source = 'auto_search'

        # 检查所有审批状态
        status_results = check_all_approval_status(git_links, sql_links, apollo_links, compact=compact)

        total_links = len(git_links) + len(sql_links) + len(apollo_links)
        if total_links > 0:
            logger.info(f"wiki请求处理完成: Git {len(git_links)}, SQL {len(sql_links)}, Apollo {len(apollo_links)}")

        # 根据模式构建不同的返回数据结构
        if compact:
            # 精简模式：返回前端友好的格式
            data = {
                'status_results': status_results
            }

            # 添加wiki信息
            if source == 'specified_url':
                data['wiki_url'] = wiki_url
            else:
                data['wiki_urls'] = [urljoin("https://wiki.serviceshare.com", article) for article in articles]

            return {
                'success': True,
                'code': 200,
                'message': '分析完成',
                'data': data
            }
        else:
            # 普通模式：保持原有格式
            data = {
                'source': source,
                'status_results': status_results,
                'links': {
                    'git_links': git_links,
                    'sql_links': sql_links,
                    'apollo_links': apollo_links
                }
            }

            # 根据模式添加不同的wiki信息
            if source == 'specified_url':
                data['wiki_url'] = wiki_url
            else:
                data['wiki_urls'] = [urljoin("https://wiki.serviceshare.com", article) for article in articles]
                data['wiki_count'] = len(articles)

            return {
                'success': True,
                'data': data
            }

    except Exception as e:
        logger.error(f"处理wiki请求时出错: {str(e)}")
        return {
            'success': False,
            'error': f'处理wiki请求时出错: {str(e)}',
            'data': None
        }

if __name__ == '__main__':
    print("=== Wiki功能测试 ===")
    session = login()
    if session:
        articles = search_articles(session)
        if articles:
            links = process_articles(session, articles)
            print(links)
            print("\nGit审批链接:")
            for link in links['git_links']:
                print(f"  - {link}")

            print("\nSQL审批链接:")
            for link in links['sql_links']:
                print(f"  - {link}")

            print("\nApollo审批链接:")
            for link in links['apollo_links']:
                print(f"  - {link}")
        else:
            print("未找到符合条件的文章")
    else:
        print("登录失败或未能获取Cookie")