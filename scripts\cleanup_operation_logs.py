#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
操作日志清理脚本
清理OperationLog表中180天前的数据
用于配置到crontab中定时执行

使用方法:
1. 直接运行: python cleanup_operation_logs.py
2. 配置到crontab: 0 2 * * * /usr/bin/python3 /path/to/cleanup_operation_logs.py

作者: 系统管理员
创建时间: 2025-07-08
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
sys.path.insert(0, project_root)

# 设置环境变量，避免导入Oracle模块时的问题
os.environ.setdefault('FLASK_ENV', 'production')

from app import create_app, db
from app.models.permission import OperationLog

# 配置日志
log_file = os.path.join(script_dir, 'cleanup_operation_logs.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, mode='a', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def cleanup_old_operation_logs(days=180):
    """
    清理指定天数前的操作日志
    
    Args:
        days (int): 保留天数，默认180天
    
    Returns:
        tuple: (是否成功, 删除记录数, 错误信息)
    """
    try:
        # 计算截止日期
        cutoff_date = datetime.now() - timedelta(days=days)
        logger.info(f"开始清理 {cutoff_date.strftime('%Y-%m-%d %H:%M:%S')} 之前的操作日志")
        
        # 查询需要删除的记录数
        old_logs_count = OperationLog.query.filter(
            OperationLog.created_at < cutoff_date
        ).count()
        
        if old_logs_count == 0:
            logger.info("没有找到需要清理的操作日志")
            return True, 0, None
        
        logger.info(f"找到 {old_logs_count} 条需要清理的操作日志")
        
        # 分批删除，避免一次性删除过多数据影响数据库性能
        batch_size = 1000
        total_deleted = 0
        
        while True:
            # 查询一批需要删除的记录
            old_logs = OperationLog.query.filter(
                OperationLog.created_at < cutoff_date
            ).limit(batch_size).all()
            
            if not old_logs:
                break
            
            # 删除这批记录
            for log in old_logs:
                db.session.delete(log)
            
            # 提交事务
            db.session.commit()
            
            batch_deleted = len(old_logs)
            total_deleted += batch_deleted
            logger.info(f"已删除 {batch_deleted} 条记录，累计删除 {total_deleted} 条")
            
            # 如果这批记录少于batch_size，说明已经删除完毕
            if batch_deleted < batch_size:
                break
        
        logger.info(f"操作日志清理完成，共删除 {total_deleted} 条记录")
        return True, total_deleted, None
        
    except Exception as e:
        error_msg = f"清理操作日志时发生错误: {str(e)}"
        logger.error(error_msg)
        db.session.rollback()
        return False, 0, error_msg

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("操作日志清理脚本开始执行")
    
    try:
        # 创建Flask应用上下文
        app = create_app()
        
        with app.app_context():
            # 执行清理操作
            success, deleted_count, error = cleanup_old_operation_logs(days=180)
            
            if success:
                logger.info(f"清理操作成功完成，删除了 {deleted_count} 条记录")
                exit_code = 0
            else:
                logger.error(f"清理操作失败: {error}")
                exit_code = 1
                
    except Exception as e:
        logger.error(f"脚本执行过程中发生未预期的错误: {str(e)}")
        exit_code = 1
    
    logger.info("操作日志清理脚本执行结束")
    logger.info("=" * 50)
    
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
