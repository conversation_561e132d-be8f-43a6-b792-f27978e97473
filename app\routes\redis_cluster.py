# 在文件顶部导入新增的工具函数
from flask import Blueprint, jsonify, request, current_app
from flask_jwt_extended import jwt_required
from app.models.redis_cluster import RedisCluster
from app.models.user import User
from app.utils.redis_utils import (
    get_redis_connection, 
    get_redis_info, 
    get_redis_keys, 
    get_redis_key_value, 
    set_redis_key_value, 
    delete_redis_key
)
from app.utils.auth_utils import admin_required,permission_required
from app import db

redis_bp = Blueprint('redis', __name__)

@redis_bp.route('/redis/clusters', methods=['GET'])
@jwt_required()
def get_clusters():
    """
    获取所有Redis集群列表
    """
    try:
        clusters = RedisCluster.query.all()
        return jsonify({
            'success': True,
            'message': '获取Redis集群列表成功',
            'data': [cluster.to_dict() for cluster in clusters],
            'code': 200
        })
    except Exception as e:
        current_app.logger.error(f"获取Redis集群列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取Redis集群列表失败: {str(e)}',
            'code': 500
        }), 500

@redis_bp.route('/redis/clusters/<int:cluster_id>', methods=['GET'])
@jwt_required()
def get_cluster(cluster_id):
    """
    获取指定Redis集群详情
    """
    try:
        cluster = RedisCluster.query.get(cluster_id)
        if not cluster:
            return jsonify({
                'success': False,
                'message': 'Redis集群不存在',
                'code': 404
            }), 404
        
        return jsonify({
            'success': True,
            'message': '获取Redis集群详情成功',
            'data': cluster.to_dict(),
            'code': 200
        })
    except Exception as e:
        current_app.logger.error(f"获取Redis集群详情失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取Redis集群详情失败: {str(e)}',
            'code': 500
        }), 500

@redis_bp.route('/redis/clusters', methods=['POST'])
@jwt_required()
@admin_required
def add_cluster():
    """
    添加新的Redis集群
    """
    data = request.json
    if not data or not data.get('host') or not data.get('cluster_name'):
        return jsonify({
            'success': False,
            'message': '请提供Redis主机地址和集群名称',
            'code': 400
        }), 400
    
    try:
        # 创建新的Redis集群记录
        new_cluster = RedisCluster(
            cluster_name=data.get('cluster_name'),
            host=data.get('host'),
            port=data.get('port', 6379),
            password=data.get('password'),
            version=data.get('version'),
            is_cluster=data.get('is_cluster', False),
            description=data.get('description'),
            status=data.get('status', True)
        )
        
        db.session.add(new_cluster)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '添加Redis集群成功',
            'data': new_cluster.to_dict(),
            'code': 201
        }), 201
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"添加Redis集群失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'添加Redis集群失败: {str(e)}',
            'code': 500
        }), 500

@redis_bp.route('/redis/clusters/<int:cluster_id>', methods=['PUT'])
@jwt_required()
@admin_required
def update_cluster(cluster_id):
    """
    更新Redis集群信息
    """
    data = request.json
    if not data:
        return jsonify({
            'success': False,
            'message': '请提供要更新的数据',
            'code': 400
        }), 400
    
    try:
        cluster = RedisCluster.query.get(cluster_id)
        if not cluster:
            return jsonify({
                'success': False,
                'message': 'Redis集群不存在',
                'code': 404
            }), 404
        
        # 更新集群信息
        if 'cluster_name' in data:
            cluster.cluster_name = data['cluster_name']
        if 'host' in data:
            cluster.host = data['host']
        if 'port' in data:
            cluster.port = data['port']
        if 'password' in data:
            cluster.password = data['password']
        if 'version' in data:
            cluster.version = data['version']
        if 'is_cluster' in data:
            cluster.is_cluster = data['is_cluster']
        if 'description' in data:
            cluster.description = data['description']
        if 'status' in data:
            cluster.status = data['status']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '更新Redis集群成功',
            'data': cluster.to_dict(),
            'code': 200
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新Redis集群失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新Redis集群失败: {str(e)}',
            'code': 500
        }), 500

@redis_bp.route('/redis/clusters/<int:cluster_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_cluster(cluster_id):
    """
    删除Redis集群
    """
    try:
        cluster = RedisCluster.query.get(cluster_id)
        if not cluster:
            return jsonify({
                'success': False,
                'message': 'Redis集群不存在',
                'code': 404
            }), 404
        
        db.session.delete(cluster)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '删除Redis集群成功',
            'code': 200
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除Redis集群失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除Redis集群失败: {str(e)}',
            'code': 500
        }), 500

@redis_bp.route('/redis/clusters/<int:cluster_id>/keys', methods=['GET'])
@jwt_required()
def get_cluster_keys(cluster_id):
    """
    获取Redis集群键列表
    """
    pattern = request.args.get('pattern', '*')
    count = int(request.args.get('count', 100))
    
    try:
        # 获取Redis连接
        conn, error = get_redis_connection(cluster_id)
        if error:
            return jsonify({
                'success': False,
                'message': error,
                'code': 500
            }), 500
        
        # 获取键列表
        keys, error = get_redis_keys(conn, pattern, count)
        if error:
            return jsonify({
                'success': False,
                'message': error,
                'code': 500
            }), 500
        
        return jsonify({
            'success': True,
            'message': '获取Redis键列表成功',
            'data': keys,
            'code': 200
        })
    except Exception as e:
        current_app.logger.error(f"获取Redis键列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取Redis键列表失败: {str(e)}',
            'code': 500
        }), 500


@redis_bp.route('/redis/clusters/<int:cluster_id>/keys/<path:key>', methods=['GET'])
@jwt_required()
def get_key_value(cluster_id, key):
    """
    获取Redis键的值
    支持模糊匹配：
    - '*' 获取所有键
    - 'test*' 获取以test开头的键
    - '*test*' 获取包含test的键
    """
    try:
        # 获取Redis连接
        conn, error = get_redis_connection(cluster_id)
        if error:
            return jsonify({
                'success': False,
                'message': error,
                'code': 500
            }), 500
        
        # 检查是否包含通配符
        if '*' in key:
            # 使用模糊匹配获取键列表
            keys, error = get_redis_keys(conn, key)
            if error:
                return jsonify({
                    'success': False,
                    'message': error,
                    'code': 500
                }), 500
            
            # 获取每个键的值
            result = {}
            for k in keys:
                k_str = k.decode('utf-8') if isinstance(k, bytes) else k
                value, error = get_redis_key_value(conn, k_str)
                if not error:
                    # 获取键的TTL
                    ttl = conn.ttl(k_str)
                    value['ttl'] = ttl
                    result[k_str] = value
            
            return jsonify({
                'success': True,
                'message': '获取Redis键值成功',
                'data': result,
                'code': 200
            })
        else:
            # 获取单个键值
            value, error = get_redis_key_value(conn, key)
            if error:
                return jsonify({
                    'success': False,
                    'message': error,
                    'code': 500
                }), 500
            
            # 获取键的TTL
            ttl = conn.ttl(key)
            value['ttl'] = ttl
            
            return jsonify({
                'success': True,
                'message': '获取Redis键值成功',
                'data': value,
                'code': 200
            })
    except Exception as e:
        current_app.logger.error(f"获取Redis键值失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取Redis键值失败: {str(e)}',
            'code': 500
        }), 500

@redis_bp.route('/redis/clusters/<int:cluster_id>/keys', methods=['POST'])
@jwt_required()
@permission_required('redis.add_key')
def add_key(cluster_id):
    """
    添加或修改Redis键
    """
    data = request.json
    if not data or 'key' not in data or 'value' not in data:
        return jsonify({
            'success': False,
            'message': '请提供键名和值',
            'code': 400
        }), 400
    
    key = data.get('key')
    value = data.get('value')
    key_type = data.get('type', 'string')
    ttl = data.get('ttl')  # 可选参数，过期时间（秒）
    
    try:
        # 获取Redis连接
        conn, error = get_redis_connection(cluster_id)
        if error:
            return jsonify({
                'success': False,
                'message': error,
                'code': 500
            }), 500
        
        # 设置键值
        success, error = set_redis_key_value(conn, key, value, key_type, ttl)
        if not success:
            return jsonify({
                'success': False,
                'message': error,
                'code': 500
            }), 500
        
        return jsonify({
            'success': True,
            'message': '添加/修改Redis键成功',
            'code': 200
        })
    except Exception as e:
        current_app.logger.error(f"添加/修改Redis键失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'添加/修改Redis键失败: {str(e)}',
            'code': 500
        }), 500

@redis_bp.route('/redis/clusters/<int:cluster_id>/keys/<path:key>', methods=['DELETE'])
@jwt_required()
@permission_required('redis.delete_key')
def delete_key(cluster_id, key):
    """
    删除Redis键
    """
    try:
        # 获取Redis连接
        conn, error = get_redis_connection(cluster_id)
        if error:
            return jsonify({
                'success': False,
                'message': error,
                'code': 500
            }), 500
        
        # 删除键
        success, error = delete_redis_key(conn, key)
        if not success:
            return jsonify({
                'success': False,
                'message': error,
                'code': 404 if error == "键不存在" else 500
            }), 404 if error == "键不存在" else 500
        
        return jsonify({
            'success': True,
            'message': '删除Redis键成功',
            'code': 200
        })
    except Exception as e:
        current_app.logger.error(f"删除Redis键失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除Redis键失败: {str(e)}',
            'code': 500
        }), 500