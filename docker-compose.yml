version: '3.8'

services:
  iyunwei-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: iyunwei-api
    ports:
      - "5555:5555"
    environment:
      FLASK_ENV: production
      PYTHONPATH: /app
      LD_LIBRARY_PATH: /opt/oracle/instantclient_11_2:$LD_LIBRARY_PATH
      ORACLE_HOME: /opt/oracle/instantclient_11_2
    volumes:
      # SSH密钥映射
      - ~/.ssh:/root/.ssh:ro
      # Oracle Instant Client映射
      - /opt/oracle/instantclient_11_2:/opt/oracle/instantclient_11_2:ro
      # 日志目录映射（可选）
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - iyunwei-network
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5555/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  iyunwei-network:
    driver: bridge
    name: iyunwei-network
