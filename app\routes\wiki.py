from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.models.user import User
from app.utils.wiki_utils import process_wiki_request, extract_links_from_wiki_url, check_all_approval_status
from app.utils.auth_utils import admin_required,permission_required
import re

wiki_bp = Blueprint('wiki', __name__)

@wiki_bp.route('/wiki/analyze', methods=['POST'])
@jwt_required()
def analyze_wiki():
    """
    分析wiki页面中的审批链接
    支持两种模式：
    1. 提供wiki_url - 分析指定页面
    2. 不提供wiki_url - 自动搜索最新上线工单
    """
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401
        
        # 获取请求数据
        data = request.json or {}
        wiki_url = data.get('wiki_url', '').strip()
        compact = data.get('compact', True)  # 是否返回精简格式
        
        # 验证wiki_url格式（如果提供）
        if wiki_url:
            if not _is_valid_wiki_url(wiki_url):
                return jsonify({
                    'success': False,
                    'message': '无效的wiki URL格式',
                    'code': 400
                }), 400
        
        # 处理wiki请求
        result = process_wiki_request(wiki_url if wiki_url else None, compact=compact)
        if not result['success']:
            return jsonify({
                'success': False,
                'message': result['error'],
                'code': 500
            }), 500

        # 如果是精简模式，直接返回优化后的结构
        if compact:
            return jsonify(result)
        else:
            # 普通模式保持原有格式
            return jsonify({
                'success': True,
                'message': '分析完成',
                'data': result['data'],
                'code': 200
            })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'分析wiki页面失败: {str(e)}',
            'code': 500
        }), 500

@wiki_bp.route('/wiki/extract-links', methods=['POST'])
@jwt_required()
def extract_links():
    """
    从指定wiki页面提取审批链接（不检查状态）
    """
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401
        
        # 获取请求数据
        data = request.json or {}
        wiki_url = data.get('wiki_url', '').strip()
        
        if not wiki_url:
            return jsonify({
                'success': False,
                'message': 'wiki_url参数不能为空',
                'code': 400
            }), 400
        
        # 验证wiki_url格式
        if not _is_valid_wiki_url(wiki_url):
            return jsonify({
                'success': False,
                'message': '无效的wiki URL格式',
                'code': 400
            }), 400
        
        # 提取链接
        result = extract_links_from_wiki_url(wiki_url)
        
        if not result['success']:
            return jsonify({
                'success': False,
                'message': result['error'],
                'code': 500
            }), 500
        
        return jsonify({
            'success': True,
            'message': '提取链接成功',
            'data': {
                'wiki_url': wiki_url,
                'git_links': result['git_links'],
                'sql_links': result['sql_links'],
                'apollo_links': result['apollo_links'],
                'summary': {
                    'total_count': len(result['git_links']) + len(result['sql_links']) + len(result['apollo_links']),
                    'git_count': len(result['git_links']),
                    'sql_count': len(result['sql_links']),
                    'apollo_count': len(result['apollo_links'])
                }
            },
            'code': 200
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'提取链接失败: {str(e)}',
            'code': 500
        }), 500

@wiki_bp.route('/wiki/check-status', methods=['POST'])
@jwt_required()
def check_approval_status():
    """
    检查指定审批链接的状态
    """
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401
        
        # 获取请求数据
        data = request.json or {}
        git_links = data.get('git_links', [])
        sql_links = data.get('sql_links', [])
        apollo_links = data.get('apollo_links', [])
        
        # 验证至少有一个链接
        if not any([git_links, sql_links, apollo_links]):
            return jsonify({
                'success': False,
                'message': '至少需要提供一个审批链接',
                'code': 400
            }), 400
        
        # 检查状态
        results = check_all_approval_status(git_links, sql_links, apollo_links)
        
        return jsonify({
            'success': True,
            'message': '状态检查完成',
            'data': results,
            'code': 200
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'检查状态失败: {str(e)}',
            'code': 500
        }), 500

@wiki_bp.route('/wiki/batch-approve-sql', methods=['POST'])
@jwt_required()
@admin_required
def batch_approve_sql_workflows():
    """
    一键审批SQL工单

    要求：
    1. 只有状态是"等待运维组审批"且当前审批人是"徐世伟"或"郭亚彬"的工单才可以审批通过
    2. 前端可以传递多个SQL工单地址
    """
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401

        # 获取请求数据
        data = request.json or {}
        sql_urls = data.get('sql_urls', [])
        debug = data.get('debug', False)

        # 验证参数
        if not sql_urls:
            return jsonify({
                'success': False,
                'message': 'sql_urls参数不能为空',
                'code': 400
            }), 400

        if not isinstance(sql_urls, list):
            return jsonify({
                'success': False,
                'message': 'sql_urls必须是数组格式',
                'code': 400
            }), 400

        # 验证URL格式
        invalid_urls = []
        for url in sql_urls:
            if not _is_valid_sql_url(url):
                invalid_urls.append(url)

        if invalid_urls:
            return jsonify({
                'success': False,
                'message': f'以下URL格式无效: {", ".join(invalid_urls)}',
                'code': 400
            }), 400

        # 执行批量审批
        from app.utils.archery_utils import batch_approve_sql_workflows
        result = batch_approve_sql_workflows(sql_urls, debug=debug, max_workers=3)

        # 构建响应
        response_data = {
            'success': True,
            'message': f'批量审批完成，成功: {result["success_count"]}, 失败: {result["failed_count"]}, 跳过: {result["skipped_count"]}',
            'data': {
                'summary': {
                    'total_count': result['total_count'],
                    'success_count': result['success_count'],
                    'failed_count': result['failed_count'],
                    'skipped_count': result['skipped_count']
                },
                'details': result['details']
            },
            'code': 200
        }

        return jsonify(response_data)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'批量审批失败: {str(e)}',
            'code': 500
        }), 500

@wiki_bp.route('/wiki/pending-sql-workflows', methods=['GET'])
@jwt_required()
def get_pending_sql_workflows():
    """
    获取待运维组审批的SQL工单列表

    查询参数:
    - debug: 是否启用调试模式 (true/false)
    - max_workers: 最大并发线程数 (默认3)
    - limit: 每页显示数量 (默认14)
    - offset: 偏移量 (默认0)
    - start_date: 开始日期过滤 (YYYY-MM-DD)
    - end_date: 结束日期过滤 (YYYY-MM-DD)
    - search: 搜索关键词
    """
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401

        # 获取查询参数
        debug = request.args.get('debug', 'false').lower() == 'true'

        # 基础参数
        try:
            max_workers = int(request.args.get('max_workers', 3))
            limit = int(request.args.get('limit', 14))
            offset = int(request.args.get('offset', 0))
        except (ValueError, TypeError):
            return jsonify({
                'success': False,
                'message': '参数格式错误：max_workers、limit、offset必须是有效的数字',
                'code': 400
            }), 400

        # 过滤参数
        start_date = request.args.get('start_date', '').strip()
        end_date = request.args.get('end_date', '').strip()
        search = request.args.get('search', '').strip()

        # 验证日期格式
        if start_date:
            try:
                from datetime import datetime
                datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': 'start_date格式错误，应为YYYY-MM-DD',
                    'code': 400
                }), 400

        if end_date:
            try:
                from datetime import datetime
                datetime.strptime(end_date, '%Y-%m-%d')
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': 'end_date格式错误，应为YYYY-MM-DD',
                    'code': 400
                }), 400

        # 获取待审批工单列表
        from app.utils.archery_utils import get_pending_ops_approval_workflows
        result = get_pending_ops_approval_workflows(
            debug=debug,
            max_workers=max_workers,
            limit=limit,
            offset=offset,
            start_date=start_date,
            end_date=end_date,
            search=search
        )

        if result['success']:
            return jsonify({
                'success': True,
                'message': result['message'],
                'data': {
                    'summary': {
                        'total_found': result['total_found'],
                        'pending_ops_approval': result['pending_ops_approval'],
                        'total_count': result.get('total_count', 0),
                        'filtered_count': result.get('filtered_count', 0)
                    },
                    'pagination': {
                        'limit': limit,
                        'offset': offset,
                        'has_more': result.get('total_count', 0) > (offset + limit)
                    },
                    'workflows': result['workflows']
                },
                'code': 200
            })
        else:
            return jsonify({
                'success': False,
                'message': f'获取待审批工单失败: {result["error"]}',
                'code': 500
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取待审批工单失败: {str(e)}',
            'code': 500
        }), 500

@wiki_bp.route('/wiki/approve-sql-workflow', methods=['POST'])
@jwt_required()
@permission_required('wiki.approve_sql_workflow_by_id')
def approve_sql_workflow_by_id():
    """
    根据工单ID一键执行审批（要求是待运维组审批的状态）
    """
    try:
        # 获取当前用户信息
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '未授权的访问',
                'code': 401
            }), 401

        # 获取请求数据
        data = request.json or {}
        workflow_id = data.get('workflow_id')
        debug = data.get('debug', False)

        # 验证参数
        if not workflow_id:
            return jsonify({
                'success': False,
                'message': 'workflow_id参数不能为空',
                'code': 400
            }), 400

        # 验证workflow_id格式
        try:
            workflow_id = int(workflow_id)
        except (ValueError, TypeError):
            return jsonify({
                'success': False,
                'message': 'workflow_id必须是有效的数字',
                'code': 400
            }), 400

        # 执行审批
        from app.utils.archery_utils import approve_single_sql_workflow_by_id
        result = approve_single_sql_workflow_by_id(workflow_id, debug=debug)

        if result['success']:
            return jsonify({
                'success': True,
                'message': f'工单 {workflow_id} 审批成功',
                'data': {
                    'workflow_id': result['workflow_id'],
                    'message': result['message'],
                    'previous_status': result.get('previous_status'),
                    'action': result['action']
                },
                'code': 200
            })
        elif result.get('action') == 'skipped':
            return jsonify({
                'success': False,
                'message': f'工单 {workflow_id} 无法审批: {result["error"]}',
                'data': {
                    'workflow_id': result['workflow_id'],
                    'current_status': result.get('current_status'),
                    'status_code': result.get('status_code'),
                    'action': result['action']
                },
                'code': 400
            }), 400
        else:
            return jsonify({
                'success': False,
                'message': f'工单 {workflow_id} 审批失败: {result["error"]}',
                'data': {
                    'workflow_id': result['workflow_id'],
                    'action': result['action']
                },
                'code': 500
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'审批工单失败: {str(e)}',
            'code': 500
        }), 500

def _is_valid_sql_url(url):
    """
    验证SQL工单URL格式

    Args:
        url (str): 要验证的URL

    Returns:
        bool: 是否为有效的SQL工单URL
    """
    try:
        # 检查是否为sql.serviceshare.com域名的detail页面
        pattern = r'^https?://sql\.serviceshare\.com/detail/\d+/?$'
        return bool(re.match(pattern, url))
    except Exception:
        return False

def _is_valid_wiki_url(url):
    """
    验证wiki URL格式
    
    Args:
        url (str): 要验证的URL
        
    Returns:
        bool: 是否为有效的wiki URL
    """
    try:
        # 检查是否为wiki.serviceshare.com域名
        pattern = r'^https?://wiki\.serviceshare\.com/pages/viewpage\.action\?pageId=\d+$'
        return bool(re.match(pattern, url))
    except Exception:
        return False
