import random
import string
from io import BytesIO
import base64
from PIL import Image, ImageDraw, ImageFont, ImageFilter
import os


def generate_captcha(length=4):
    # 生成随机字符（排除易混淆字符）
    chars = ''.join([c for c in string.ascii_uppercase + string.digits
                     if c not in ['0', 'O', '1', 'I', 'L', '9', '6']])
    captcha_text = ''.join(random.choice(chars) for _ in range(length))

    # 创建RGB主图像
    width, height = 160, 60
    image = Image.new('RGB', (width, height), color=(255, 255, 255))
    draw = ImageDraw.Draw(image)

    # 加载字体（略，保持原有逻辑）

    # 修正通道问题：创建RGBA临时图像
    for i, char in enumerate(captcha_text):
        # 创建带透明通道的临时图像
        char_image = Image.new('RGBA', (width, height), (255, 255, 255, 0))
        char_draw = ImageDraw.Draw(char_image)

        # 在临时图像上绘制字符
        font_size = 40
        font_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'fonts', 'arial.ttf')
        font = ImageFont.truetype(font_path, font_size)
        char_draw.text((10 + i * 30, 15), char, font=font, fill=(0, 0, 0, 255))

        # 旋转字符
        char_image = char_image.rotate(random.randint(-30, 30), expand=0, resample=Image.BICUBIC)

        # 合并图像时转换通道模式
        image.paste(char_image, (0, 0), char_image)  # 使用alpha通道作为mask

    # 添加干扰线（保持RGB模式）
    for _ in range(5):
        x1 = random.randint(0, width)
        y1 = random.randint(0, height)
        x2 = random.randint(0, width)
        y2 = random.randint(0, height)
        draw.line([(x1, y1), (x2, y2)], fill=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)))

    # 转换为base64（保持PNG格式）
    buffer = BytesIO()
    image.save(buffer, format='PNG')
    img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

    return captcha_text, f"data:image/png;base64,{img_base64}"