import requests
import logging
from urllib.parse import urlparse
import json
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime

# 导入配置
try:
    from config import APOLLO_CONFIG
except ImportError:
    # 如果无法导入配置，使用默认值
    APOLLO_CONFIG = {
        'base_url': 'https://apollo.serviceshare.com',
        'api_base_url': 'https://apollo.serviceshare.com/openapi/v1',
        'api_token': None
    }

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置调试日志级别
def set_debug_logging():
    """设置调试日志级别"""
    # 设置根logger和当前模块logger的级别
    logging.getLogger().setLevel(logging.DEBUG)
    logger.setLevel(logging.DEBUG)
    # 为requests库也设置调试级别
    logging.getLogger('requests').setLevel(logging.DEBUG)
    logging.getLogger('urllib3').setLevel(logging.DEBUG)
    print("🔧 已启用Apollo调试模式，将显示详细日志")

class ApolloConfigChecker:
    """Apollo配置检查器 - 使用官方Open API"""

    def __init__(self, debug=False, token=None):
        self.session = requests.Session()
        self.debug = debug

        # 如果启用debug模式，设置调试日志
        if self.debug:
            set_debug_logging()
            logger.debug("启用Apollo检查器调试模式")

        # 从配置文件获取URL和token
        self.base_url = APOLLO_CONFIG.get('base_url', 'https://apollo.serviceshare.com')
        self.api_base_url = APOLLO_CONFIG.get('api_base_url', f"{self.base_url}/openapi/v1")

        # 优先使用传入的token，否则使用配置文件中的token
        self.token = token or APOLLO_CONFIG.get('api_token')

        # 设置API请求头
        self.session.headers.update({
            'Content-Type': 'application/json;charset=UTF-8',
            'Accept': 'application/json',
            'User-Agent': 'Apollo-Config-Checker/1.0'
        })

        # 如果有token，设置Authorization头 - Apollo使用特定的token格式
        if self.token:
            # Apollo Open API 使用 token 作为Authorization头
            self.session.headers['Authorization'] = self.token
            self._debug_print(f"使用API Token: {self.token[:10]}...")
        else:
            self._debug_print("未设置API Token")
    
    def _debug_print(self, message):
        """调试输出"""
        if self.debug:
            # 同时输出到日志和控制台
            debug_msg = f"[Apollo DEBUG] {message}"
            logger.debug(debug_msg)
            print(debug_msg)  # 直接输出到控制台

    def set_token(self, token):
        """
        设置API Token

        Args:
            token (str): Apollo Open API Token
        """
        self.token = token
        self.session.headers['Authorization'] = token
        self._debug_print(f"设置API Token: {token[:10]}...")

    def test_api_connection(self):
        """
        测试API连接

        Returns:
            bool: 连接是否成功
        """
        if not self.token:
            logger.error("未设置API Token")
            return False

        try:
            # 测试获取应用信息接口
            test_url = f"{self.api_base_url}/apps"
            self._debug_print(f"测试API连接，URL: {test_url}")
            response = self.session.get(test_url, timeout=30)

            self._debug_print(f"API连接测试响应状态码: {response.status_code}")
            if self.debug and response.status_code != 200:
                self._debug_print(f"API连接测试响应内容: {response.text[:500]}")

            if response.status_code == 200:
                try:
                    # 尝试解析JSON响应
                    result = response.json()
                    logger.info("Apollo API连接成功")
                    self._debug_print(f"API连接成功，响应内容: {json.dumps(result, indent=2, ensure_ascii=False)[:200]}...")
                    return True
                except json.JSONDecodeError as e:
                    logger.error(f"Apollo API响应不是有效的JSON: {str(e)}")
                    self._debug_print(f"API响应解析失败: {str(e)}, 响应内容: {response.text[:200]}")
                    return False
            elif response.status_code == 401:
                logger.error("Apollo API Token无效或已过期")
                return False
            elif response.status_code == 403:
                logger.error("Apollo API Token权限不足")
                return False
            else:
                logger.error(f"Apollo API连接失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"测试Apollo API连接时发生异常: {str(e)}")
            return False

    def parse_config_url(self, url):
        """
        解析Apollo配置URL，提取appid、env、cluster、namespace等参数

        Args:
            url (str): Apollo配置URL

        Returns:
            dict: 解析出的参数
        """
        try:
            # 解析URL
            parsed_url = urlparse(url)

            # 从fragment中提取参数 (例如: #/appid=carpub-system&env=SBY&cluster=default&namespace=application)
            fragment = parsed_url.fragment
            if fragment.startswith('/'):
                fragment = fragment[1:]

            # 解析参数
            params = {}
            if '&' in fragment:
                for param in fragment.split('&'):
                    if '=' in param:
                        key, value = param.split('=', 1)
                        params[key] = value
            elif '=' in fragment:
                # 处理只有一个参数的情况
                key, value = fragment.split('=', 1)
                params[key] = value

            # 设置默认值
            if 'cluster' not in params:
                params['cluster'] = 'default'
            if 'namespace' not in params:
                params['namespace'] = 'application'

            # 设置默认环境 - 如果未指定环境，使用PRO作为默认环境
            if 'env' not in params or not params['env']:
                params['env'] = 'PRO'
                self._debug_print(f"未指定环境，使用默认环境: PRO")

            self._debug_print(f"解析URL参数: {params}")
            return params

        except Exception as e:
            logger.error(f"解析Apollo URL失败: {str(e)}")
            return {}

    def check_config_status(self, url):
        """
        检查指定URL的Apollo配置状态 - 使用官方API

        Args:
            url (str): Apollo配置URL

        Returns:
            dict: 检查结果
        """
        try:
            # 解析URL参数
            self._debug_print(f"开始检查配置状态，URL: {url}")
            params = self.parse_config_url(url)
            if not params:
                self._debug_print("URL解析失败")
                return {
                    'success': False,
                    'url': url,
                    'error': 'URL解析失败',
                    'status': '解析失败',
                    'has_pending_release': False
                }

            appid = params.get('appid', '')
            env = params.get('env', '')
            cluster = params.get('cluster', 'default')
            namespace = params.get('namespace', 'application')

            self._debug_print(f"解析出的参数: appid={appid}, env={env}, cluster={cluster}, namespace={namespace}")

            if not self.token:
                self._debug_print("未设置API Token")
                return {
                    'success': False,
                    'url': url,
                    'error': '未设置API Token',
                    'status': 'Token缺失',
                    'has_pending_release': False
                }

            # 使用Apollo Open API检查配置状态
            self._debug_print("开始检查待发布状态...")
            has_pending_release = self._check_pending_release_via_api(env, appid, cluster, namespace)

            # 获取配置状态信息
            self._debug_print("获取配置状态信息...")
            status_info = self._get_namespace_info_via_api(env, appid, cluster, namespace)

            # 在调试模式下输出更多信息
            self._debug_print(f"检测结果: has_pending_release = {has_pending_release}")
            self._debug_print(f"状态信息: {json.dumps(status_info, indent=2, ensure_ascii=False)}")

            return {
                'success': True,
                'url': url,
                'appid': appid,
                'env': env,
                'cluster': cluster,
                'namespace': namespace,
                'has_pending_release': has_pending_release,
                'status': '有待发布配置' if has_pending_release else '无待发布配置',
                'status_info': status_info,
                'check_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        except Exception as e:
            logger.error(f"检查Apollo配置状态失败: {url}, 错误: {str(e)}")
            return {
                'success': False,
                'url': url,
                'error': f'检查失败: {str(e)}',
                'status': '检查失败',
                'has_pending_release': False
            }

    def _check_pending_release_via_api(self, env, appid, cluster, namespace):
        """
        通过Apollo Open API检查是否有待发布的配置

        Args:
            env (str): 环境
            appid (str): 应用ID
            cluster (str): 集群名称
            namespace (str): 命名空间

        Returns:
            bool: 是否有待发布配置
        """
        try:
            self._debug_print(f"检查待发布状态: {env}/{appid}/{cluster}/{namespace}")

            # 方法1: 检查namespace锁定状态
            # 如果namespace被锁定，说明有人正在编辑，可能有待发布的配置
            self._debug_print("检查namespace锁定状态...")
            lock_status = self._get_namespace_lock_status(env, appid, cluster, namespace)
            self._debug_print(f"锁定状态: {lock_status}")
            if lock_status and lock_status.get('isLocked', False):
                self._debug_print(f"Namespace被锁定，锁定人: {lock_status.get('lockedBy', 'unknown')}")
                return True

            # 方法2: 比较当前配置和已发布配置
            # 获取当前namespace的配置
            self._debug_print("获取当前namespace配置...")
            current_config = self._get_namespace_config(env, appid, cluster, namespace)
            if not current_config:
                self._debug_print("无法获取当前配置")
                return False

            self._debug_print(f"当前配置: {json.dumps(current_config, indent=2, ensure_ascii=False)}")

            # 获取最新发布的配置
            self._debug_print("获取最新发布配置...")
            latest_release = self._get_latest_release(env, appid, cluster, namespace)
            if not latest_release:
                # 如果没有发布记录，但有有效配置项，说明有待发布的配置
                valid_items = self._filter_valid_config_items(current_config.get('items', []))
                if valid_items:
                    self._debug_print("没有发布记录但有有效配置项，存在待发布配置")
                    return True
                self._debug_print("没有发布记录且没有有效配置项")
                return False

            self._debug_print(f"最新发布: {json.dumps(latest_release, indent=2, ensure_ascii=False)}")

            # 比较配置项 - 过滤掉空配置项和注释项
            current_items = self._filter_valid_config_items(current_config.get('items', []))
            released_items = latest_release.get('configurations', {})

            self._debug_print(f"当前有效配置项: {json.dumps(current_items, indent=2, ensure_ascii=False)}")
            self._debug_print(f"已发布配置项: {json.dumps(released_items, indent=2, ensure_ascii=False)}")

            # 检查是否有差异
            if current_items != released_items:
                self._debug_print("当前配置与已发布配置存在差异")
                self._debug_print(f"当前配置项数: {len(current_items)}")
                self._debug_print(f"已发布配置项数: {len(released_items)}")

                # 详细比较差异
                for key in set(current_items.keys()) | set(released_items.keys()):
                    current_val = current_items.get(key, '<不存在>')
                    released_val = released_items.get(key, '<不存在>')
                    if current_val != released_val:
                        self._debug_print(f"配置项差异 - {key}: 当前='{current_val}', 已发布='{released_val}'")

                return True

            self._debug_print("当前配置与已发布配置一致，无待发布配置")
            return False

        except Exception as e:
            logger.error(f"通过API检查待发布状态时发生错误: {str(e)}")
            self._debug_print(f"检查待发布状态异常: {str(e)}")
            return False

    def _filter_valid_config_items(self, items):
        """
        过滤有效的配置项，排除空配置项和纯注释项

        Args:
            items (list): 配置项列表

        Returns:
            dict: 有效的配置项字典 {key: value}
        """
        valid_items = {}

        for item in items:
            key = item.get('key', '').strip()
            value = item.get('value', '').strip()
            comment = item.get('comment', '').strip()

            # 跳过空key的配置项
            if not key:
                self._debug_print(f"跳过空key配置项: value='{value}', comment='{comment}'")
                continue

            # 跳过纯注释项（key以#开头的项）
            if key.startswith('#'):
                self._debug_print(f"跳过注释项: key='{key}', value='{value}'")
                continue

            # 跳过key和value都为空的项
            if not key and not value:
                self._debug_print(f"跳过空配置项: key='{key}', value='{value}'")
                continue

            # 添加有效配置项
            valid_items[key] = value

        self._debug_print(f"过滤后的有效配置项数量: {len(valid_items)} (原始数量: {len(items)})")
        return valid_items

    def _get_namespace_lock_status(self, env, appid, cluster, namespace):
        """
        获取namespace锁定状态

        Returns:
            dict: 锁定状态信息
        """
        try:
            # 确保env参数不为空
            if not env:
                self._debug_print("环境参数为空，无法获取锁定状态")
                return None

            # 构建URL
            url = f"{self.api_base_url}/envs/{env}/apps/{appid}/clusters/{cluster}/namespaces/{namespace}/lock"

            self._debug_print(f"请求锁定状态URL: {url}")
            response = self.session.get(url, timeout=30)
            self._debug_print(f"锁定状态响应状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self._debug_print(f"锁定状态响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    return result
                except json.JSONDecodeError as e:
                    self._debug_print(f"解析锁定状态JSON失败: {str(e)}, 响应内容: {response.text[:200]}")
                    return None
            else:
                self._debug_print(f"获取锁定状态失败，状态码: {response.status_code}")
                self._debug_print(f"错误响应内容: {response.text[:200]}")
                return None

        except Exception as e:
            self._debug_print(f"获取锁定状态异常: {str(e)}")
            return None

    def _get_namespace_config(self, env, appid, cluster, namespace):
        """
        获取namespace当前配置

        Returns:
            dict: 配置信息
        """
        try:
            # 确保env参数不为空
            if not env:
                self._debug_print("环境参数为空，无法获取namespace配置")
                return None

            # 构建URL
            url = f"{self.api_base_url}/envs/{env}/apps/{appid}/clusters/{cluster}/namespaces/{namespace}"

            self._debug_print(f"请求namespace配置URL: {url}")
            response = self.session.get(url, timeout=30)
            self._debug_print(f"namespace配置响应状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self._debug_print(f"namespace配置响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    return result
                except json.JSONDecodeError as e:
                    self._debug_print(f"解析namespace配置JSON失败: {str(e)}, 响应内容: {response.text[:200]}")
                    return None
            else:
                self._debug_print(f"获取namespace配置失败，状态码: {response.status_code}")
                self._debug_print(f"错误响应内容: {response.text[:200]}")
                return None

        except Exception as e:
            self._debug_print(f"获取namespace配置异常: {str(e)}")
            return None

    def _get_latest_release(self, env, appid, cluster, namespace):
        """
        获取最新发布的配置

        Returns:
            dict: 发布信息
        """
        try:
            # 确保env参数不为空
            if not env:
                self._debug_print("环境参数为空，无法获取最新发布")
                return None

            # 构建URL
            url = f"{self.api_base_url}/envs/{env}/apps/{appid}/clusters/{cluster}/namespaces/{namespace}/releases/latest"

            self._debug_print(f"请求最新发布URL: {url}")
            response = self.session.get(url, timeout=30)
            self._debug_print(f"最新发布响应状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    self._debug_print(f"最新发布响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    return result
                except json.JSONDecodeError as e:
                    self._debug_print(f"解析最新发布JSON失败: {str(e)}, 响应内容: {response.text[:200]}")
                    return None
            elif response.status_code == 404:
                # 404表示没有发布记录
                self._debug_print("没有找到发布记录")
                return None
            else:
                self._debug_print(f"获取最新发布失败，状态码: {response.status_code}")
                self._debug_print(f"错误响应内容: {response.text[:200]}")
                return None

        except Exception as e:
            self._debug_print(f"获取最新发布异常: {str(e)}")
            return None

    def _get_namespace_info_via_api(self, env, appid, cluster, namespace):
        """
        通过API获取namespace状态信息

        Args:
            env (str): 环境
            appid (str): 应用ID
            cluster (str): 集群名称
            namespace (str): 命名空间

        Returns:
            dict: 状态信息
        """
        try:
            status_info = {}

            # 获取namespace配置信息
            config_info = self._get_namespace_config(env, appid, cluster, namespace)
            if config_info:
                status_info['namespace_name'] = config_info.get('namespaceName', namespace)
                status_info['format'] = config_info.get('format', 'properties')
                status_info['is_public'] = config_info.get('isPublic', False)
                status_info['comment'] = config_info.get('comment', '')
                status_info['config_count'] = len(config_info.get('items', []))
                status_info['last_modified_time'] = config_info.get('dataChangeLastModifiedTime', '')
                status_info['last_modified_by'] = config_info.get('dataChangeLastModifiedBy', '')

            # 获取锁定状态
            lock_info = self._get_namespace_lock_status(env, appid, cluster, namespace)
            if lock_info:
                status_info['is_locked'] = lock_info.get('isLocked', False)
                status_info['locked_by'] = lock_info.get('lockedBy', '')

            # 获取最新发布信息
            release_info = self._get_latest_release(env, appid, cluster, namespace)
            if release_info:
                status_info['latest_release_name'] = release_info.get('name', '')
                status_info['latest_release_time'] = release_info.get('dataChangeCreatedTime', '')
                status_info['latest_release_by'] = release_info.get('dataChangeCreatedBy', '')
                status_info['released_config_count'] = len(release_info.get('configurations', {}))
            else:
                status_info['latest_release_name'] = '无发布记录'
                status_info['released_config_count'] = 0

            return status_info

        except Exception as e:
            logger.error(f"获取状态信息时发生错误: {str(e)}")
            return {}

    def check_multiple_environments(self, appid, environments=['SBY', 'LTY', 'PRO'], cluster='default'):
        """
        检查指定应用在多个环境中的配置状态

        Args:
            appid (str): 应用ID
            environments (list): 环境列表
            cluster (str): 集群名称

        Returns:
            dict: 检查结果
        """
        logger.info(f"开始检查应用 {appid} 在多个环境中的配置状态")

        results = {
            'appid': appid,
            'cluster': cluster,
            'environments': {},
            'summary': {
                'total_envs': len(environments),
                'pending_envs': 0,
                'success_count': 0,
                'error_count': 0
            }
        }

        # 使用线程池并发检查多个环境
        with ThreadPoolExecutor(max_workers=3) as executor:
            # 构建URL并提交任务
            futures = {}
            for env in environments:
                url = f"{self.base_url}/config.html#/appid={appid}&env={env}&cluster={cluster}"
                future = executor.submit(self.check_config_status, url)
                futures[future] = env

            # 收集结果
            for future in futures:
                env = futures[future]
                try:
                    result = future.result()
                    results['environments'][env] = result

                    if result['success']:
                        results['summary']['success_count'] += 1
                        if result.get('has_pending_release', False):
                            results['summary']['pending_envs'] += 1
                    else:
                        results['summary']['error_count'] += 1

                except Exception as e:
                    logger.error(f"检查环境 {env} 时发生异常: {str(e)}")
                    results['environments'][env] = {
                        'success': False,
                        'env': env,
                        'error': f'检查异常: {str(e)}',
                        'status': '检查异常',
                        'has_pending_release': False
                    }
                    results['summary']['error_count'] += 1

        if results['summary']['total_envs'] > 0:
            logger.info(f"多环境配置状态检查完成: 成功 {results['summary']['success_count']}, 失败 {results['summary']['error_count']}, 待发布 {results['summary']['pending_envs']}")

        return results

    def check_urls_batch(self, urls):
        """
        批量检查多个Apollo配置URL

        Args:
            urls (list): URL列表

        Returns:
            dict: 批量检查结果
        """
        if len(urls) > 0:
            logger.info(f"开始批量检查 {len(urls)} 个Apollo配置URL")

        results = {
            'total_urls': len(urls),
            'results': [],
            'summary': {
                'success_count': 0,
                'error_count': 0,
                'pending_count': 0
            }
        }

        # 使用线程池并发检查
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(self.check_config_status, url) for url in urls]

            for i, future in enumerate(futures):
                try:
                    result = future.result()
                    results['results'].append(result)

                    if result['success']:
                        results['summary']['success_count'] += 1
                        if result.get('has_pending_release', False):
                            results['summary']['pending_count'] += 1
                    else:
                        results['summary']['error_count'] += 1

                except Exception as e:
                    logger.error(f"检查URL {i+1} 时发生异常: {str(e)}")
                    results['results'].append({
                        'success': False,
                        'url': urls[i] if i < len(urls) else '未知URL',
                        'error': f'检查异常: {str(e)}',
                        'status': '检查异常',
                        'has_pending_release': False
                    })
                    results['summary']['error_count'] += 1

        if len(urls) > 0:
            logger.info(f"批量检查完成: 成功 {results['summary']['success_count']}, 失败 {results['summary']['error_count']}, 待发布 {results['summary']['pending_count']}")

        return results


def check_apollo_config_status(url, debug=True, token=None):
    """
    检查单个Apollo配置状态的便捷函数

    Args:
        url (str): Apollo配置URL
        debug (bool): 是否启用调试模式 (默认True)
        token (str): Apollo Open API Token (可选，默认使用配置文件中的token)

    Returns:
        dict: 检查结果
    """
    checker = ApolloConfigChecker(debug=debug, token=token)

    # 检查是否有可用的token
    if not checker.token:
        return {
            'success': False,
            'url': url,
            'error': '未提供API Token且配置文件中未设置默认token',
            'status': 'Token缺失',
            'has_pending_release': False
        }

    # 测试API连接
    if not checker.test_api_connection():
        return {
            'success': False,
            'url': url,
            'error': 'API连接失败',
            'status': 'API连接失败',
            'has_pending_release': False
        }

    # 检查配置状态
    return checker.check_config_status(url)


def check_apollo_multiple_envs(appid, environments=['SBY', 'LTY', 'PRO'], cluster='default', debug=True, token=None):
    """
    检查指定应用在多个环境中的配置状态的便捷函数

    Args:
        appid (str): 应用ID
        environments (list): 环境列表
        cluster (str): 集群名称
        debug (bool): 是否启用调试模式 (默认True)
        token (str): Apollo Open API Token (可选，默认使用配置文件中的token)

    Returns:
        dict: 检查结果
    """
    checker = ApolloConfigChecker(debug=debug, token=token)

    # 检查是否有可用的token
    if not checker.token:
        return {
            'success': False,
            'appid': appid,
            'error': '未提供API Token且配置文件中未设置默认token',
            'environments': {}
        }

    # 测试API连接
    if not checker.test_api_connection():
        return {
            'success': False,
            'appid': appid,
            'error': 'API连接失败',
            'environments': {}
        }

    # 检查多环境配置状态
    return checker.check_multiple_environments(appid, environments, cluster)


def check_apollo_urls_batch(urls, debug=True, token=None):
    """
    批量检查多个Apollo配置URL的便捷函数

    Args:
        urls (list): URL列表
        debug (bool): 是否启用调试模式 (默认True)
        token (str): Apollo Open API Token

    Returns:
        dict: 批量检查结果
    """
    checker = ApolloConfigChecker(debug=debug, token=token)

    # 如果没有提供token，返回错误
    if not token:
        return {
            'success': False,
            'error': '未提供API Token',
            'results': []
        }

    # 测试API连接
    if not checker.test_api_connection():
        return {
            'success': False,
            'error': 'API连接失败',
            'results': []
        }

    # 批量检查
    return checker.check_urls_batch(urls)


if __name__ == '__main__':
    # 测试代码
    print("=== Apollo配置检查工具测试 ===")
    print("注意: 需要提供有效的Apollo Open API Token才能进行测试")

    # 这里需要替换为实际的token
    test_token = "your_apollo_api_token_here"

    if test_token == "your_apollo_api_token_here":
        print("❌ 请先设置有效的Apollo API Token")
        print("获取Token的步骤:")
        print("1. 联系Apollo管理员")
        print("2. 在 https://apollo.serviceshare.com/open/manage.html 创建第三方应用")
        print("3. 获取生成的token")
        print("4. 将token替换到代码中的test_token变量")
    else:
        # 测试单个URL检查
        test_url = "https://apollo.serviceshare.com/config.html#/appid=carpub-system&env=SBY&cluster=default"
        print(f"\n1. 测试单个URL检查: {test_url}")
        result = check_apollo_config_status(test_url, debug=True, token=test_token)
        print(f"结果: {result}")

        # 测试多环境检查
        print(f"\n2. 测试多环境检查: carpub-system")
        result = check_apollo_multiple_envs("carpub-system", debug=True, token=test_token)
        print(f"结果: {result}")

        # 测试批量URL检查
        test_urls = [
            "https://apollo.serviceshare.com/config.html#/appid=carpub-system&env=SBY&cluster=default",
            "https://apollo.serviceshare.com/config.html#/appid=carpub-system&env=LTY&cluster=default",
            "https://apollo.serviceshare.com/config.html#/appid=carpub-system&env=PRO&cluster=default"
        ]
        print(f"\n3. 测试批量URL检查: {len(test_urls)} 个URL")
        result = check_apollo_urls_batch(test_urls, debug=True, token=test_token)
        print(f"结果: {result}")
