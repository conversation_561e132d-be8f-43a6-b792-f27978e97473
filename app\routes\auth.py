from flask import Blueprint, request, jsonify, current_app
from werkzeug.security import check_password_hash
from datetime import datetime, timedelta
from app.models.user import User
from app.utils.captcha import generate_captcha
import jwt
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from app import db

auth_bp = Blueprint('auth', __name__)

# 生成验证码令牌的公共函数
def generate_captcha_token(captcha_text):
    return jwt.encode(
        {
            'captcha': captcha_text,
            'exp': datetime.utcnow() + timedelta(minutes=5)
        },
        current_app.config['JWT_SECRET_KEY'],
        algorithm=current_app.config['JWT_ALGORITHM']
    )

@auth_bp.route('/captcha', methods=['GET'])
def captcha():
    try:
        # 生成验证码及令牌
        captcha_text, captcha_image = generate_captcha()
        captcha_token = generate_captcha_token(captcha_text)

        return jsonify({
            'code': 0,
            'data': {
                'captcha_token': captcha_token,
                'image': captcha_image,
                'expires_in': 300  # 前端倒计时使用
            }
        })
    except Exception as e:
        current_app.logger.error(f"验证码生成失败: {str(e)}")
        return jsonify({'code': 500, 'message': '系统繁忙，请稍后重试'}), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    input_captcha = data.get('captcha', '').upper()
    captcha_token = data.get('captcha_token')

    # 验证码校验
    try:
        payload = jwt.decode(
            captcha_token,
            current_app.config['JWT_SECRET_KEY'],
            algorithms=[current_app.config['JWT_ALGORITHM']]
        )
        if datetime.utcnow().timestamp() > payload['exp']:
            return jsonify({'code': 2, 'message': '验证码已过期'}), 401
        if input_captcha != payload['captcha'].upper():
            return jsonify({'code': 1, 'message': '验证码错误'}), 401
    except jwt.ExpiredSignatureError:
        return jsonify({'code': 2, 'message': '验证码已过期'}), 401
    except Exception as e:
        current_app.logger.warning(f"Invalid captcha token: {str(e)}")
        return jsonify({'code': 2, 'message': '验证码无效'}), 401

    # 用户认证
    user = User.query.filter_by(username=username).first()
    if not user or not check_password_hash(user.password_hash, password):
        return jsonify({'code': 1, 'message': '用户名或密码错误'}), 401

    # 生成访问令牌（包含用户ID和验证码指纹）
    access_token = create_access_token(
        identity=user.id,
        additional_claims={
            'captcha_fp': jwt.encode(
                {'captcha': payload['captcha']},
                current_app.config['JWT_SECRET_KEY'],
                algorithm=current_app.config['JWT_ALGORITHM']
            )[:32]
        },
        expires_delta=timedelta(hours=4)
    )

    try:
        user.last_login_time = datetime.utcnow() + timedelta(hours=8)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新登录时间失败: {str(e)}")
        return jsonify({'code': 3, 'message': '服务器内部错误'}), 500

    response = jsonify({
        'code': 0,
        'data': {
            'token': access_token,
            'user_info': {
                'user_id': user.id,
                'username': user.username
            }
        }
    })

    
    return response

# 受保护的路由示例
@auth_bp.route('/check_session', methods=['GET'])
@jwt_required()
def check_session():
    current_user = User.query.get(get_jwt_identity())
    return jsonify({
        'code': 0,
        'data': {
            'user_id': current_user.id,
            'username': current_user.username
        }
    })