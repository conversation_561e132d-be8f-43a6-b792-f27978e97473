from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
from app.utils.fail2ban_utils import get_fail2ban_data, get_enabled_jails, ban_ip, unban_ip
from app.utils.auth_utils import permission_required

fail2ban_bp = Blueprint('fail2ban', __name__, url_prefix='/fail2ban')

@fail2ban_bp.route('', methods=['GET'])
@jwt_required()
def fail2ban():
    """获取fail2ban数据"""
    try:
        banned_ips = get_fail2ban_data().get("banned_ips", [])
        jails = get_enabled_jails().get("enabled_jails", [])
        
        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'banned_ips': banned_ips,
                'jails': jails
            }
        })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'获取失败: {str(e)}',
            'data': None
        })

@fail2ban_bp.route('/ban', methods=['POST'])
@jwt_required()
@permission_required('fail2ban.ban_ip')
def ban_ip_route():
    """封禁IP"""
    data = request.get_json()
    ip = data.get('ip')
    jail = data.get('jail')
    
    if not ip or not jail:
        return jsonify({
            'code': 400,
            'message': '参数错误，需要提供IP和jail',
            'data': None
        })
    
    try:
        result = ban_ip(ip, jail)
        if result:
            return jsonify({
                'code': 200,
                'message': f'成功封禁IP: {ip}',
                'data': None
            })
        else:
            return jsonify({
                'code': 500,
                'message': f'封禁IP失败: {ip}',
                'data': None
            })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'封禁操作出错: {str(e)}',
            'data': None
        })

@fail2ban_bp.route('/unban', methods=['POST'])
@jwt_required()
@permission_required('fail2ban.unban_ip')
def unban_ip_route():
    """解封IP"""
    data = request.get_json()
    ip = data.get('ip')
    jail = data.get('jail')
    
    if not ip or not jail:
        return jsonify({
            'code': 400,
            'message': '参数错误，需要提供IP和jail',
            'data': None
        })
    
    try:
        result = unban_ip(ip, jail)
        if result:
            return jsonify({
                'code': 200,
                'message': f'成功解封IP: {ip}',
                'data': None
            })
        else:
            return jsonify({
                'code': 500,
                'message': f'解封IP失败: {ip}',
                'data': None
            })
    except Exception as e:
        return jsonify({
            'code': 500,
            'message': f'解封操作出错: {str(e)}',
            'data': None
        })