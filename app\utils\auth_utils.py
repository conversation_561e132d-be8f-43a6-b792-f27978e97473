from functools import wraps
from flask import jsonify, request, current_app
from flask_jwt_extended import current_user
from app import db
import time
import json

def admin_required(f):
    """
    检查当前用户是否为管理员的装饰器
    如果不是管理员，则返回403错误
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_admin:
            return jsonify({"code": 403, "message": "权限不足"}), 403
        return f(*args, **kwargs)
    return decorated_function

def self_or_admin_required(user_id_param='id'):
    """
    检查当前用户是否为管理员或操作自己账户的装饰器
    如果既不是管理员也不是操作自己的账户，则返回403错误

    :param user_id_param: 路由中用户ID参数的名称
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user_id = kwargs.get(user_id_param)
            if not current_user.is_admin and current_user.id != user_id:
                return jsonify({"code": 403, "message": "权限不足，普通用户只能修改自己的信息"}), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def permission_required(permission_name=None):
    """
    权限检查装饰器，支持自动日志记录

    :param permission_name: 权限名称，如果不提供则自动生成（模块名.函数名）
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            start_time = time.time()

            # 自动生成权限名称
            if permission_name is None:
                module_name = f.__module__.split('.')[-1]  # 获取模块名
                func_name = f.__name__
                perm_name = f"{module_name}.{func_name}"
            else:
                perm_name = permission_name
                module_name = perm_name.split('.')[0] if '.' in perm_name else 'unknown'
                func_name = perm_name.split('.')[-1] if '.' in perm_name else perm_name

            # 管理员拥有所有权限
            if current_user.is_admin:
                has_permission = True
            else:
                # 检查用户是否有该权限
                has_permission = check_user_permission(current_user.id, perm_name)

            # 记录操作日志
            log_data = {
                'user_id': current_user.id,
                'permission_name': perm_name,
                'module': module_name,
                'function_name': func_name,
                'request_method': request.method,
                'request_path': request.path,
                'ip_address': get_client_ip(),
                'user_agent': request.headers.get('User-Agent', '')[:500]
            }

            # 记录请求参数
            try:
                if request.method == 'GET':
                    log_data['request_params'] = json.dumps(dict(request.args))
                elif request.method in ['POST', 'PUT', 'PATCH']:
                    if request.is_json:
                        log_data['request_params'] = json.dumps(request.get_json())
                    else:
                        log_data['request_params'] = json.dumps(dict(request.form))
            except Exception:
                log_data['request_params'] = 'Failed to serialize request params'

            if not has_permission:
                # 记录权限不足的日志
                log_data.update({
                    'status_code': 403,
                    'response_message': '权限不足',
                    'execution_time': time.time() - start_time
                })
                log_operation(**log_data)
                return jsonify({"code": 403, "message": "权限不足"}), 403

            try:
                # 执行原函数
                result = f(*args, **kwargs)

                # 记录成功的日志
                execution_time = time.time() - start_time
                if isinstance(result, tuple) and len(result) == 2:
                    response_data, status_code = result
                    log_data.update({
                        'status_code': status_code,
                        'response_message': 'Success',
                        'execution_time': execution_time
                    })
                else:
                    log_data.update({
                        'status_code': 200,
                        'response_message': 'Success',
                        'execution_time': execution_time
                    })

                log_operation(**log_data)
                return result

            except Exception as e:
                # 记录异常日志
                execution_time = time.time() - start_time
                log_data.update({
                    'status_code': 500,
                    'response_message': f'Error: {str(e)}',
                    'execution_time': execution_time
                })
                log_operation(**log_data)
                raise

        return decorated_function
    return decorator

def check_user_permission(user_id, permission_name):
    """检查用户是否有指定权限"""
    try:
        from app.models.permission import UserPermission, Permission

        # 查询用户是否有该权限
        permission_query = db.session.query(UserPermission).join(Permission).filter(
            UserPermission.user_id == user_id,
            Permission.name == permission_name,
            UserPermission.is_active == True
        ).first()

        return permission_query is not None
    except Exception as e:
        current_app.logger.error(f"检查用户权限时出错: {str(e)}")
        return False

def log_operation(**kwargs):
    """记录操作日志"""
    try:
        from app.models.permission import OperationLog

        log_entry = OperationLog(**kwargs)
        db.session.add(log_entry)
        db.session.commit()
    except Exception as e:
        current_app.logger.error(f"记录操作日志时出错: {str(e)}")
        db.session.rollback()

def get_client_ip():
    """获取客户端IP地址"""
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr