from app import db
from datetime import datetime

class NginxFileCheck(db.Model):
    __tablename__ = 'nginx_file_checks'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    file_path = db.Column(db.String(255), nullable=False, index=True)
    check_time = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    is_consistent = db.Column(db.<PERSON>, nullable=False)
    check_result = db.Column(db.Text, nullable=True)  # 存储JSON格式的详细对比结果
    
    def __repr__(self):
        return f'<NginxFileCheck {self.file_path} at {self.check_time}>'